package ghostcat.display.movieclip
{
   import flash.display.FrameLabel;
   import flash.media.Sound;
   import flash.media.SoundTransform;
   import flash.utils.Dictionary;
   import ghostcat.display.GBase;
   import ghostcat.events.MovieEvent;
   import ghostcat.events.TickEvent;
   import ghostcat.util.Tick;
   import ghostcat.util.Util;
   import ghostcat.util.core.AbstractUtil;
   import ghostcat.util.core.Handler;
   
   public class GMovieClipBase extends GBase
   {
      
      public static var labelHandlers:Dictionary = new Dictionary();
      
      protected var _labels:Array;
      
      private var _currentFrame:int = 1;
      
      protected var _totalFrames:int = 1;
      
      private var _frameRate:Number = NaN;
      
      private var numLoops:int = -1;
      
      private var nextLabels:Array = [];
      
      protected var curLabelIndex:int = 0;
      
      protected var frameTimer:int = 0;
      
      public var linkMovieClips:Array;
      
      public var playOnce:Boolean = true;
      
      public var resetLabel:Boolean = true;
      
      public function GMovieClipBase(param1:* = null, param2:Boolean = true, param3:Boolean = false)
      {
         AbstractUtil.preventConstructor(this,GMovieClipBase);
         super(param1,param2);
         this.enabledTick = true;
         this.paused = param3;
      }
      
      public static function registerHandler(param1:String, param2:Handler) : void
      {
         labelHandlers[param1] = param2;
      }
      
      public static function registerSound(param1:String, param2:Sound, param3:int = 1, param4:Number = 1, param5:Number = 0) : void
      {
         registerHandler(param1,new Handler(param2.play,[0,param3,new SoundTransform(param4,param5)]));
      }
      
      public function get frameRate() : Number
      {
         if(!isNaN(this._frameRate))
         {
            return this._frameRate;
         }
         if(!isNaN(Tick.frameRate))
         {
            return Tick.frameRate;
         }
         if(stage)
         {
            return stage.frameRate;
         }
         return NaN;
      }
      
      public function set frameRate(param1:Number) : void
      {
         this._frameRate = param1;
      }
      
      public function getLabelIndex(param1:String) : int
      {
         var _loc2_:int = 0;
         while(_loc2_ < this.labels.length)
         {
            if((this.labels[_loc2_] as FrameLabel).name == param1)
            {
               return _loc2_;
            }
            _loc2_++;
         }
         return -1;
      }
      
      public function hasLabel(param1:String) : Boolean
      {
         return this.getLabelIndex(param1) != -1;
      }
      
      public function setLoop(param1:int) : void
      {
         this.numLoops = param1;
      }
      
      public function setLabel(param1:String, param2:int = -1, param3:Boolean = true) : void
      {
         var _loc5_:MovieEvent = null;
         if(param3)
         {
            this.nextLabels = [];
         }
         var _loc4_:int = this.getLabelIndex(param1);
         if(_loc4_ != -1)
         {
            this.numLoops = param2;
            if(!this.resetLabel && _loc4_ == this.curLabelIndex)
            {
               return;
            }
            this.currentFrame = this.frameRate >= 0 ? this.getLabelStart(_loc4_) : this.getLabelEnd(_loc4_);
            this.curLabelIndex = _loc4_;
            _loc5_ = new MovieEvent(MovieEvent.MOVIE_START);
            _loc5_.labelName = param1;
            dispatchEvent(_loc5_);
            if(GMovieClipBase.labelHandlers[param1])
            {
               (GMovieClipBase.labelHandlers[param1] as Handler).call();
            }
         }
         else
         {
            _loc5_ = new MovieEvent(MovieEvent.MOVIE_END);
            _loc5_.labelName = param1;
            dispatchEvent(_loc5_);
            _loc5_ = new MovieEvent(MovieEvent.MOVIE_ERROR);
            _loc5_.labelName = param1;
            dispatchEvent(_loc5_);
         }
      }
      
      public function queueLabel(param1:String, param2:int = -1) : void
      {
         this.nextLabels.push([param1,param2]);
      }
      
      public function clearQueue() : void
      {
         this.nextLabels = [];
      }
      
      public function reset() : void
      {
         this.clearQueue();
         if(Boolean(this.labels) && this.labels.length > 0)
         {
            this.setLabel(this.labels[0].name,-1);
         }
      }
      
      override protected function tickHandler(param1:TickEvent) : void
      {
         var _loc2_:MovieEvent = null;
         if(this.frameRate == 0 || this.numLoops == 0 || this.totalFrames <= 1)
         {
            return;
         }
         this.frameTimer -= param1.interval;
         while(this.numLoops != 0 && this.frameTimer < 0)
         {
            if(this.hasReachedLabelEnd())
            {
               if(this.numLoops > 0)
               {
                  --this.numLoops;
               }
               if(this.numLoops == 0)
               {
                  _loc2_ = new MovieEvent(MovieEvent.MOVIE_END);
                  _loc2_.labelName = this.curLabelName;
                  dispatchEvent(_loc2_);
                  if(this.nextLabels.length > 0)
                  {
                     this.setLabel(this.nextLabels[0][0],this.nextLabels[0][1],false);
                     this.nextLabels.splice(0,1);
                  }
                  else
                  {
                     _loc2_ = new MovieEvent(MovieEvent.MOVIE_EMPTY);
                     _loc2_.labelName = this.curLabelName;
                     dispatchEvent(_loc2_);
                     this.frameTimer = 0;
                  }
               }
               else
               {
                  this.loopBackToStart();
               }
            }
            else
            {
               this.nextFrame();
            }
            this.frameTimer += 1000 / Math.abs(this.frameRate);
         }
      }
      
      public function get frameInLabel() : int
      {
         return this.currentFrame - this.getLabelStart(this.curLabelIndex) + 1;
      }
      
      public function set frameInLabel(param1:int) : void
      {
         this.currentFrame = this.getLabelStart(this.curLabelIndex) + param1 - 1;
      }
      
      public function loopBackToStart() : void
      {
         this.currentFrame = this.frameRate >= 0 ? this.getLabelStart(this.curLabelIndex) : this.getLabelEnd(this.curLabelIndex);
      }
      
      private function hasReachedLabelEnd() : Boolean
      {
         if(this.frameRate >= 0)
         {
            return this.currentFrame >= this.getLabelEnd(this.curLabelIndex);
         }
         return this.currentFrame <= this.getLabelStart(this.curLabelIndex);
      }
      
      private function getLabelStart(param1:int) : int
      {
         return Boolean(this.labels) && this.labels.length > 0 ? int(this.labels[param1].frame) : 1;
      }
      
      private function getLabelEnd(param1:int) : int
      {
         if(Boolean(this.labels) && param1 + 1 < this.labels.length)
         {
            return this.labels[param1 + 1].frame - 1;
         }
         return this.totalFrames;
      }
      
      override public function destory() : void
      {
         paused = false;
         super.destory();
      }
      
      public function hasLabels() : Boolean
      {
         return Boolean(this.labels) && this.labels.length > 0;
      }
      
      public function get labels() : Array
      {
         return this._labels;
      }
      
      public function get curLabelName() : String
      {
         var _loc1_:int = int(this.labels.length - 1);
         while(_loc1_ >= 0)
         {
            if((this.labels[_loc1_] as FrameLabel).frame <= this.currentFrame)
            {
               return (this.labels[_loc1_] as FrameLabel).name;
            }
            _loc1_--;
         }
         return null;
      }
      
      public function get currentFrame() : int
      {
         return this._currentFrame;
      }
      
      public function set currentFrame(param1:int) : void
      {
         var _loc2_:GMovieClipBase = null;
         this._currentFrame = param1;
         if(this.linkMovieClips)
         {
            for each(_loc2_ in this.linkMovieClips)
            {
               _loc2_.currentFrame = param1;
            }
         }
      }
      
      public function get totalFrames() : int
      {
         return this._totalFrames;
      }
      
      public function nextFrame() : void
      {
         if(this.frameRate >= 0)
         {
            ++this.currentFrame;
         }
         else
         {
            --this.currentFrame;
         }
      }
      
      public function linkTo(param1:GMovieClipBase) : void
      {
         this.paused = true;
         if(!param1.linkMovieClips)
         {
            param1.linkMovieClips = [];
         }
         param1.linkMovieClips.push(this);
      }
      
      public function removeLinkFrom(param1:GMovieClipBase) : void
      {
         this.paused = false;
         Util.remove(param1.linkMovieClips,this);
         if(param1.linkMovieClips.length == 0)
         {
            param1.linkMovieClips = null;
         }
      }
   }
}

