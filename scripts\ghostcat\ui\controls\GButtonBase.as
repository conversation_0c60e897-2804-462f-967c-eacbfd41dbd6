package ghostcat.ui.controls
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextFieldAutoSize;
   import ghostcat.display.GBase;
   import ghostcat.display.movieclip.GMovieClipBase;
   import ghostcat.events.ActionEvent;
   import ghostcat.ui.layout.Padding;
   import ghostcat.util.core.AbstractUtil;
   
   public class GButtonBase extends GBase
   {
      
      public static const LABEL_UP:String = "up";
      
      public static const LABEL_OVER:String = "over";
      
      public static const LABEL_DOWN:String = "down";
      
      public static const LABEL_DISABLED:String = "disabled";
      
      public static const LABEL_SELECTED_UP:String = "selectedUp";
      
      public static const LABEL_SELECTED_OVER:String = "selectedOver";
      
      public static const LABEL_SELECTED_DOWN:String = "selectedDown";
      
      public static const LABEL_SELECTED_DISABLED:String = "selectedDisabled";
      
      public static const LABELS:Array = [[<PERSON>BEL_UP,LABEL_SELECTED_UP],[<PERSON><PERSON><PERSON>_OVER,LABEL_SELECTED_OVER],[LABEL_DOWN,LABEL_SELECTED_DOWN],[LABEL_DISABLED,LABEL_SELECTED_DISABLED]];
      
      private const UP:int = 0;
      
      private const OVER:int = 1;
      
      private const DOWN:int = 2;
      
      private const DISABLED:int = 3;
      
      protected var movie:GMovieClipBase;
      
      private var defaultSkin:*;
      
      private var _toggle:Boolean;
      
      private var _mouseDown:Boolean = false;
      
      private var _mouseOver:Boolean = false;
      
      private var _autoSize:String = "none";
      
      public var labelTextField:GText;
      
      public var labelField:String;
      
      public var textPadding:Padding;
      
      public var separateTextField:Boolean = false;
      
      public var enabledAdjustContextSize:Boolean = false;
      
      public var autoRefreshLabelField:Boolean = true;
      
      public var action:String;
      
      public var trackAsMenu:Boolean = false;
      
      public const upState:GButtonState = new GButtonState();
      
      public const overState:GButtonState = new GButtonState();
      
      public const downState:GButtonState = new GButtonState();
      
      public const disabledState:GButtonState = new GButtonState();
      
      public const selectedUpState:GButtonState = new GButtonState();
      
      public const selectedOverState:GButtonState = new GButtonState();
      
      public const selectedDownState:GButtonState = new GButtonState();
      
      public const selectedDisabledState:GButtonState = new GButtonState();
      
      public var buttonStates:Object = {
         "up":this.upState,
         "over":this.overState,
         "down":this.downState,
         "disabled":this.disabledState,
         "selectedUp":this.selectedUpState,
         "selectedOver":this.selectedOverState,
         "selectedDown":this.selectedDownState,
         "selectedDisabled":this.selectedDisabledState
      };
      
      public var toggle:Boolean = false;
      
      public function GButtonBase(param1:* = null, param2:Boolean = true, param3:Boolean = false, param4:Padding = null)
      {
         AbstractUtil.preventConstructor(this,GButtonBase);
         if(param4)
         {
            this.textPadding = param4;
         }
         this.separateTextField = param3;
         super(param1,param2);
         this.mouseChildren = false;
      }
      
      public function get autoSize() : String
      {
         return this._autoSize;
      }
      
      public function set autoSize(param1:String) : void
      {
         this._autoSize = param1;
         if(this.labelTextField)
         {
            this.labelTextField.autoSize = param1;
         }
      }
      
      public function get mouseDown() : Boolean
      {
         return this._mouseDown;
      }
      
      public function get mouseOver() : Boolean
      {
         return this._mouseOver;
      }
      
      public function get label() : String
      {
         return this.labelField ? data[this.labelField] : data;
      }
      
      public function set label(param1:String) : void
      {
         if(this.labelField)
         {
            if(super.data == null)
            {
               super.data = new Object();
            }
            super.data[this.labelField] = param1;
         }
         else
         {
            this.data = param1;
         }
      }
      
      override public function set data(param1:*) : void
      {
         super.data = param1;
         if(this.labelTextField)
         {
            this.labelTextField.text = this.label;
         }
         else if(this.autoRefreshLabelField)
         {
            this.refreshLabelField();
         }
      }
      
      override public function set enabled(param1:Boolean) : void
      {
         if(super.enabled == param1)
         {
            return;
         }
         this.mouseChildren = this.mouseEnabled = super.enabled = param1;
         this.tweenTo(this.UP);
      }
      
      public function refreshLabelField() : void
      {
         if(!this.label)
         {
            return;
         }
         if(this.labelTextField)
         {
            this.labelTextField.destory();
         }
         this.labelTextField = new GText(content,false,this.separateTextField,this.textPadding);
         this.labelTextField.enabledAdjustContextSize = this.enabledAdjustContextSize;
         if(this.labelTextField.autoSize == TextFieldAutoSize.NONE)
         {
            this.labelTextField.autoSize = this._autoSize;
         }
         else
         {
            this._autoSize = this.labelTextField.autoSize;
         }
         addChild(this.labelTextField);
         this.labelTextField.text = this.label;
      }
      
      override public function setContent(param1:*, param2:Boolean = true) : void
      {
         this.defaultSkin = param1;
         this.setPartConetent(param1,param2);
      }
      
      public function setPartConetent(param1:*, param2:Boolean = true) : void
      {
         super.setContent(param1,param2);
         if(this.autoRefreshLabelField)
         {
            this.refreshLabelField();
         }
         this.createMovieClip();
      }
      
      protected function createMovieClip() : void
      {
      }
      
      override public function set selected(param1:Boolean) : void
      {
         if(super.selected == param1)
         {
            return;
         }
         super.selected = param1;
         this.tweenTo(this._mouseOver ? this.OVER : this.UP);
         dispatchEvent(new Event(Event.CHANGE));
      }
      
      protected function addEvents() : void
      {
         addEventListener(MouseEvent.MOUSE_DOWN,this.mouseDownHandler);
         if(stage)
         {
            stage.addEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
         }
         addEventListener(MouseEvent.ROLL_OVER,this.rollOverHandler);
         addEventListener(MouseEvent.ROLL_OUT,this.rollOutHandler);
         addEventListener(MouseEvent.CLICK,this.clickHandler);
      }
      
      protected function removeEvents() : void
      {
         removeEventListener(MouseEvent.MOUSE_DOWN,this.mouseDownHandler);
         if(stage)
         {
            stage.removeEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
         }
         removeEventListener(MouseEvent.ROLL_OVER,this.rollOverHandler);
         removeEventListener(MouseEvent.ROLL_OUT,this.rollOutHandler);
         removeEventListener(MouseEvent.CLICK,this.clickHandler);
      }
      
      override protected function init() : void
      {
         super.init();
         this.addEvents();
         this.tweenTo(this.UP);
      }
      
      protected function tweenTo(param1:int) : void
      {
         if(!enabled)
         {
            param1 = this.DISABLED;
         }
         var _loc2_:String = LABELS[param1][int(selected)];
         var _loc3_:GButtonState = this.buttonStates[_loc2_];
         if(_loc3_)
         {
            _loc3_.parse(this);
         }
         if(content && this.movie && Boolean(this.movie.labels))
         {
            if(this.movie.hasLabel(this.movie.curLabelName + "-" + _loc2_))
            {
               this.movie.setLabel(this.movie.curLabelName + "-" + _loc2_,1);
               this.movie.queueLabel(_loc2_,-1);
            }
            else if(this.movie.hasLabel("*-" + _loc2_))
            {
               this.movie.setLabel("*-" + _loc2_,1);
               this.movie.queueLabel(_loc2_,-1);
            }
            else
            {
               this.movie.setLabel(_loc2_,-1);
            }
         }
      }
      
      protected function mouseDownHandler(param1:MouseEvent) : void
      {
         if(this._mouseDown)
         {
            return;
         }
         this.tweenTo(this.DOWN);
         this._mouseDown = true;
      }
      
      protected function mouseUpHandler(param1:MouseEvent) : void
      {
         if(!this._mouseDown)
         {
            return;
         }
         this.tweenTo(this._mouseOver ? this.OVER : this.UP);
         this._mouseDown = false;
         if(this.trackAsMenu)
         {
            dispatchEvent(new MouseEvent(MouseEvent.CLICK));
         }
      }
      
      protected function rollOverHandler(param1:MouseEvent) : void
      {
         if(param1.buttonDown)
         {
            if(this.trackAsMenu || this._mouseDown)
            {
               this.tweenTo(this.DOWN);
            }
         }
         else
         {
            this.tweenTo(this.OVER);
         }
         this._mouseOver = true;
      }
      
      protected function rollOutHandler(param1:MouseEvent) : void
      {
         this.tweenTo(this.UP);
         this._mouseOver = false;
      }
      
      protected function clickHandler(param1:MouseEvent) : void
      {
         var _loc2_:ActionEvent = null;
         if(this.toggle)
         {
            this.selected = !selected;
         }
         if(this.action)
         {
            _loc2_ = new ActionEvent(ActionEvent.ACTION);
            _loc2_.action = this.action;
            dispatchEvent(_loc2_);
         }
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         this.removeEvents();
         if(this.labelTextField)
         {
            this.labelTextField.destory();
         }
         if(this.movie)
         {
            this.movie.destory();
         }
         super.destory();
      }
   }
}

