package ghostcat.events
{
   import flash.events.Event;
   
   public class TimeLineEvent extends Event
   {
      
      public static const LABEL_CHANGED:String = "label_changed";
      
      public static const TIMELINE_END:String = "timeline_end";
      
      public static const TIMELINE_START:String = "timeline_start";
      
      public var prevLabel:String;
      
      public var label:String;
      
      public var prevFrame:int;
      
      public var frame:int;
      
      public function TimeLineEvent(param1:String, param2:Boolean = false, param3:Boolean = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         var _loc1_:TimeLineEvent = new TimeLineEvent(type,bubbles,cancelable);
         _loc1_.prevLabel = this.prevLabel;
         _loc1_.label = this.label;
         _loc1_.prevFrame = this.prevFrame;
         _loc1_.frame = this.frame;
         return _loc1_;
      }
   }
}

