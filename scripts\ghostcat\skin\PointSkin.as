package ghostcat.skin
{
   import flash.display.Sprite;
   import ghostcat.parse.DisplayParse;
   import ghostcat.parse.graphics.GraphicsEllipse;
   import ghostcat.parse.graphics.GraphicsFill;
   import ghostcat.parse.graphics.GraphicsLineStyle;
   
   public class PointSkin extends Sprite
   {
      
      public function PointSkin(param1:Number = 3, param2:uint = 0, param3:Number = 0, param4:uint = 16777215)
      {
         super();
         DisplayParse.create([new GraphicsLineStyle(param3,param2),new GraphicsFill(param4,0.5),new GraphicsEllipse(0,0,param1 + param1,param1 + param1)]).parse(this);
      }
   }
}

