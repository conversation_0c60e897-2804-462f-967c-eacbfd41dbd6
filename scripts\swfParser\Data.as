package swfParser
{
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   import flash.utils.Endian;
   import flash.utils.IDataInput;
   
   public class Data implements IDataInput
   {
      
      protected var _data:ByteArray;
      
      protected var _bitBuff:int;
      
      protected var _bitPos:int;
      
      public function Data(param1:ByteArray)
      {
         super();
         this._data = param1;
         this._data.endian = Endian.LITTLE_ENDIAN;
         this.synchBits();
      }
      
      public function synchBits() : void
      {
         this._bitBuff = 0;
         this._bitPos = 0;
      }
      
      public function readSBits(param1:uint) : int
      {
         var _loc2_:* = int(this.readUBits(param1));
         if((_loc2_ & 1 << param1 - 1) != 0)
         {
            _loc2_ |= -1 << param1;
         }
         return _loc2_;
      }
      
      public function readUBits(param1:uint) : uint
      {
         var _loc4_:int = 0;
         if(param1 == 0)
         {
            return 0;
         }
         var _loc2_:int = int(param1);
         var _loc3_:* = 0;
         if(this._bitPos == 0)
         {
            this._bitBuff = this._data.readUnsignedByte();
            this._bitPos = 8;
         }
         while(true)
         {
            _loc4_ = _loc2_ - this._bitPos;
            if(_loc4_ <= 0)
            {
               break;
            }
            _loc3_ |= this._bitBuff << _loc4_;
            _loc2_ -= this._bitPos;
            this._bitBuff = this._data.readUnsignedByte();
            this._bitPos = 8;
         }
         _loc3_ |= this._bitBuff >> -_loc4_;
         this._bitPos -= _loc2_;
         this._bitBuff &= 255 >> 8 - this._bitPos;
         return _loc3_;
      }
      
      public function readBoolean() : Boolean
      {
         this.synchBits();
         return this._data.readBoolean();
      }
      
      public function readByte() : int
      {
         this.synchBits();
         return this.readByte();
      }
      
      public function readBytes(param1:ByteArray, param2:uint = 0, param3:uint = 0) : void
      {
         this.synchBits();
         this._data.readBytes(param1,param2,param3);
      }
      
      public function readDouble() : Number
      {
         var _loc1_:ByteArray = new ByteArray();
         var _loc2_:ByteArray = new ByteArray();
         this._data.readBytes(_loc1_,0,8);
         _loc2_.length = 8;
         _loc2_[0] = _loc1_[3];
         _loc2_[1] = _loc1_[2];
         _loc2_[2] = _loc1_[1];
         _loc2_[3] = _loc1_[0];
         _loc2_[4] = _loc1_[7];
         _loc2_[5] = _loc1_[6];
         _loc2_[6] = _loc1_[5];
         _loc2_[7] = _loc1_[4];
         _loc2_.position = 0;
         return _loc2_.readDouble();
      }
      
      public function readMatrix() : Matrix
      {
         var _loc1_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc7_:uint = 0;
         this.synchBits();
         if(this.readUBits(1) == 1)
         {
            _loc7_ = this.readUBits(5);
            _loc1_ = this.readSBits(_loc7_) / 65536;
            _loc2_ = this.readSBits(_loc7_) / 65536;
         }
         else
         {
            _loc1_ = 1;
            _loc2_ = 1;
         }
         if(this.readUBits(1) == 1)
         {
            _loc7_ = this.readUBits(5);
            _loc3_ = this.readSBits(_loc7_) / 65536;
            _loc4_ = this.readSBits(_loc7_) / 65536;
         }
         else
         {
            _loc3_ = 0;
            _loc4_ = 0;
         }
         _loc7_ = this.readUBits(5);
         _loc5_ = this.readSBits(_loc7_) * 0.05;
         _loc6_ = this.readSBits(_loc7_) * 0.05;
         return new Matrix(_loc1_,_loc3_,_loc4_,_loc2_,_loc5_,_loc6_);
      }
      
      public function readRect() : Rectangle
      {
         var _loc1_:uint = this.readUBits(5);
         var _loc2_:Number = this.readSBits(_loc1_) * 0.05;
         var _loc3_:Number = this.readSBits(_loc1_) * 0.05;
         var _loc4_:Number = this.readSBits(_loc1_) * 0.05;
         var _loc5_:Number = this.readSBits(_loc1_) * 0.05;
         this.synchBits();
         return new Rectangle(_loc2_,_loc4_,_loc3_ - _loc2_,_loc5_ - _loc4_);
      }
      
      public function readFloat() : Number
      {
         this.synchBits();
         return this._data.readFloat();
      }
      
      public function readInt() : int
      {
         this.synchBits();
         return this._data.readInt();
      }
      
      public function readMultiByte(param1:uint, param2:String) : String
      {
         this.synchBits();
         return this._data.readMultiByte(param1,param2);
      }
      
      public function readObject() : *
      {
         this.synchBits();
         return this._data.readObject();
      }
      
      public function readShort() : int
      {
         this.synchBits();
         return this._data.readShort();
      }
      
      public function readUnsignedByte() : uint
      {
         this.synchBits();
         return this._data.readUnsignedByte();
      }
      
      public function readUnsignedInt() : uint
      {
         this.synchBits();
         return this._data.readUnsignedInt();
      }
      
      public function readUnsignedShort() : uint
      {
         this.synchBits();
         return this._data.readUnsignedShort();
      }
      
      public function readUTF() : String
      {
         this.synchBits();
         return this._data.readUTF();
      }
      
      public function readUTFBytes(param1:uint) : String
      {
         this.synchBits();
         return this._data.readUTFBytes(param1);
      }
      
      public function readString() : String
      {
         var _loc1_:uint = 0;
         var _loc2_:ByteArray = new ByteArray();
         while(true)
         {
            _loc1_ = this.readUnsignedByte();
            if(_loc1_ == 0)
            {
               break;
            }
            _loc2_.writeByte(_loc1_);
         }
         _loc2_.position = 0;
         return _loc2_.readMultiByte(_loc2_.length,"UTF-8");
      }
      
      public function get data() : ByteArray
      {
         return this._data;
      }
      
      public function set data(param1:ByteArray) : void
      {
         this._data = param1;
         this.synchBits();
      }
      
      public function get position() : int
      {
         return this._data.position;
      }
      
      public function set position(param1:int) : void
      {
         this._data.position = param1;
      }
      
      public function get bytesAvailable() : uint
      {
         return this._data.bytesAvailable;
      }
      
      public function get endian() : String
      {
         return this._data.endian;
      }
      
      public function set endian(param1:String) : void
      {
         this._data.endian = param1;
      }
      
      public function get objectEncoding() : uint
      {
         return this._data.objectEncoding;
      }
      
      public function set objectEncoding(param1:uint) : void
      {
         this._data.objectEncoding = param1;
      }
      
      public function get length() : uint
      {
         return this._data.length;
      }
   }
}

