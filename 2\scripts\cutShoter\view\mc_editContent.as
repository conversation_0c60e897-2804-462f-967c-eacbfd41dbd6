package cutShoter.view
{
   import cutShoter.events.EventManager;
   import flash.display.*;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol136")]
   public class mc_editContent extends MovieClip
   {
      
      public var btn_close:SimpleButton;
      
      public var mc_cutContent:MovieClip;
      
      public var btn_OK:SimpleButton;
      
      public var mc_plcontent:MovieClip;
      
      public var btn_Cancle:SimpleButton;
      
      public var mc_title:MovieClip;
      
      private var _image:Bitmap;
      
      public function mc_editContent()
      {
         super();
         if(!stage)
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         else
         {
            this.init();
         }
      }
      
      private function init(param1:Event = null) : void
      {
         if(param1)
         {
            this.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         this.btn_OK.addEventListener(MouseEvent.CLICK,this.okHandler);
         this.btn_Cancle.addEventListener(MouseEvent.CLICK,this.closeHandler);
         this.btn_close.addEventListener(MouseEvent.CLICK,this.closeHandler);
      }
      
      private function closeHandler(param1:MouseEvent) : void
      {
         dispatchEvent(new EventManager(EventManager.COMMENTCANCLE));
         if(parent)
         {
            parent.removeChild(this);
         }
      }
      
      private function okHandler(param1:MouseEvent) : void
      {
         dispatchEvent(new EventManager(EventManager.COMMENTCOMPLETED));
         if(parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      public function setImage(param1:BitmapData) : void
      {
         this._image = new Bitmap(param1);
         this._image.width = this.mc_cutContent.width - 2;
         this._image.height = this.mc_cutContent.height - 2;
         this._image.x = this._image.y = 2;
         if(!this.mc_cutContent.getChildByName("_image"))
         {
            this.mc_cutContent.addChild(this._image);
         }
      }
      
      public function getCommentConent() : String
      {
         return TextField(this.mc_plcontent["editArea"]).text;
      }
      
      public function getImageData() : BitmapData
      {
         return this._image.bitmapData;
      }
   }
}

