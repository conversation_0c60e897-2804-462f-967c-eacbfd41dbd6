package ghostcat.parse.display
{
   import flash.display.DisplayObject;
   import flash.geom.Rectangle;
   import ghostcat.parse.DisplayParse;
   
   public class Grid9Parse extends DisplayParse
   {
      
      public var x:Number;
      
      public var y:Number;
      
      public var width:Number;
      
      public var height:Number;
      
      public function Grid9Parse(param1:Number, param2:Number, param3:Number, param4:Number)
      {
         super();
         this.x = param1;
         this.y = param2;
         this.width = param3;
         this.height = param4;
      }
      
      override public function parseDisplay(param1:DisplayObject) : void
      {
         super.parseDisplay(param1);
         param1.scale9Grid = new Rectangle(this.x,this.y,this.width,this.height);
      }
   }
}

