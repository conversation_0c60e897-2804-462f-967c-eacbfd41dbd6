package ghostcat.display.screenshot
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.BlendMode;
   import flash.display.Shape;
   import flash.display.Sprite;
   import flash.display.Stage;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Rectangle;
   import flash.text.TextFormat;
   import flash.ui.Keyboard;
   import ghostcat.display.GBase;
   import ghostcat.display.graphics.ScaleBox;
   import ghostcat.display.graphics.SelectRect;
   import ghostcat.events.TickEvent;
   import ghostcat.parse.display.SimpleRectParse;
   import ghostcat.parse.graphics.GraphicsFill;
   import ghostcat.ui.controls.GText;
   import ghostcat.ui.layout.Padding;
   import ghostcat.util.display.BitmapUtil;
   
   public class ScreenShotPanel extends GBase
   {
      
      public var rHandler:Function;
      
      public var result:BitmapData;
      
      protected var screen:Bitmap;
      
      protected var layer:Sprite;
      
      protected var selectRect:Sprite;
      
      protected var scaleBox:ScaleBox;
      
      protected var panel:GText;
      
      protected var buttonBar:GBase;
      
      protected var _screenWidth:Number;
      
      protected var _screenHeight:Number;
      
      public function ScreenShotPanel(param1:Function, param2:Stage, param3:Number, param4:Number, param5:Array = null)
      {
         super();
         this.rHandler = param1;
         this._screenWidth = param3;
         this._screenHeight = param4;
         this.screen = new Bitmap(ScreenShotUtil.showScreen(param2,this._screenWidth,this._screenHeight,param5));
         addChild(this.screen);
         param2.addChild(this);
         this.layer = new Sprite();
         this.layer.blendMode = BlendMode.LAYER;
         addChild(this.layer);
         var _loc6_:Shape = new Shape();
         _loc6_.graphics.beginFill(0,0.5);
         _loc6_.graphics.drawRect(0,0,this._screenWidth,this._screenHeight);
         _loc6_.graphics.endFill();
         this.layer.addChild(_loc6_);
         this.selectRect = new SelectRect(this._screenWidth,this._screenHeight,this.selectHandler,null,new GraphicsFill(16777215));
         this.selectRect.blendMode = BlendMode.ERASE;
         (this.selectRect as SelectRect).createTo(this.layer);
         this.panel = new GText(new SimpleRectParse(100,100,NaN,2684354560).createShape(),true,true,new Padding(2,2,2,2));
         this.panel.applyTextFormat(new TextFormat(null,null,16777215),true);
         this.panel.enabledAdjustContextSize = true;
         this.panel.text = "点击屏幕选取范围";
         addChild(this.panel);
         this.enabledTick = true;
      }
      
      protected function selectHandler(param1:Rectangle) : void
      {
         trace("rect = " + param1.toString());
         (this.selectRect as SelectRect).destory();
         this.selectRect = new Sprite();
         this.selectRect.graphics.beginFill(16777215);
         this.selectRect.graphics.drawRect(0,0,param1.width,param1.height);
         this.selectRect.graphics.endFill();
         this.selectRect.blendMode = BlendMode.ERASE;
         this.selectRect.x = param1.x;
         this.selectRect.y = param1.y;
         this.layer.addChild(this.selectRect);
         this.scaleBox = new ScaleBox(this.selectRect,this._screenWidth,this._screenHeight);
         this.scaleBox.fillControl.doubleClickEnabled = true;
         this.scaleBox.fillControl.addEventListener(MouseEvent.DOUBLE_CLICK,this.doubleClickHandler);
         addChild(this.scaleBox);
         this.buttonBar = new ControlButtonBar(this.doubleClickHandler,this.cancelHandler);
         addChild(this.buttonBar);
         stage.addEventListener(KeyboardEvent.KEY_DOWN,this.keyDownHandler);
      }
      
      private function doubleClickHandler(param1:MouseEvent) : void
      {
         this.getBitmapData();
      }
      
      private function cancelHandler(param1:MouseEvent) : void
      {
         this.destory();
      }
      
      private function keyDownHandler(param1:KeyboardEvent) : void
      {
         if(param1.keyCode == Keyboard.ENTER)
         {
            this.getBitmapData();
         }
         if(param1.keyCode == Keyboard.ESCAPE)
         {
            this.destory();
         }
      }
      
      private function getBitmapData() : void
      {
         this.result = BitmapUtil.clip(this.screen.bitmapData,this.scaleBox.getRect(this));
         dispatchEvent(new Event(Event.COMPLETE));
         if(this.rHandler != null)
         {
            this.rHandler(this.result);
         }
         this.destory();
      }
      
      override protected function tickHandler(param1:TickEvent) : void
      {
         var _loc2_:Rectangle = this.selectRect.getRect(this);
         if(_loc2_.isEmpty())
         {
            this.panel.x = mouseX;
            this.panel.y = mouseY - this.panel.height - 5;
         }
         else
         {
            this.panel.text = "双击或回车截图 (大小：" + _loc2_.width + "," + _loc2_.height + ")";
            this.panel.x = _loc2_.x;
            if(_loc2_.y - this.panel.height - 5 <= 0)
            {
               this.panel.y = _loc2_.y;
            }
            else
            {
               this.panel.y = _loc2_.y - this.panel.height - 5;
            }
            if(this.buttonBar)
            {
               if(_loc2_.bottom + this.buttonBar.height + 5 > this._screenHeight)
               {
                  this.buttonBar.y = _loc2_.bottom - this.buttonBar.height;
               }
               else
               {
                  this.buttonBar.y = _loc2_.bottom + 5;
               }
               this.buttonBar.x = _loc2_.right - this.buttonBar.width;
            }
         }
      }
      
      override public function destory() : void
      {
         this.scaleBox.fillControl.removeEventListener(MouseEvent.DOUBLE_CLICK,this.doubleClickHandler);
         stage.removeEventListener(KeyboardEvent.KEY_DOWN,this.keyDownHandler);
         this.buttonBar.destory();
         super.destory();
         dispatchEvent(new Event("csDestory_4399"));
      }
   }
}

import flash.events.MouseEvent;
import ghostcat.display.GBase;
import ghostcat.skin.ScreenShotButton;
import ghostcat.ui.UIBuilder;
import ghostcat.ui.controls.GButton;

class ControlButtonBar extends GBase
{
   
   public var ok:GButton;
   
   public var cancel:GButton;
   
   public function ControlButtonBar(param1:Function, param2:Function)
   {
      super(ScreenShotButton);
      UIBuilder.buildAll(this);
      this.ok.toolTip = "完成截图";
      this.ok.addEventListener(MouseEvent.CLICK,param1,false,0,true);
      this.cancel.toolTip = "退出截图";
      this.cancel.addEventListener(MouseEvent.CLICK,param2,false,0,true);
   }
   
   override public function destory() : void
   {
      UIBuilder.destory(this);
      super.destory();
   }
}
