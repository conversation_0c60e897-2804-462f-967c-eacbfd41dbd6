package ghostcat.display.bitmap
{
   import flash.display.BitmapData;
   import flash.display.Graphics;
   import flash.events.IEventDispatcher;
   import flash.geom.Point;
   
   public interface IBitmapDataDrawer extends IEventDispatcher
   {
      
      function drawToBitmapData(param1:BitmapData, param2:Point) : void;
      
      function drawToShape(param1:Graphics, param2:Point) : void;
      
      function getBitmapUnderMouse(param1:Number, param2:Number) : Array;
   }
}

