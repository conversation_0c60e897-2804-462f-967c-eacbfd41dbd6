package ghostcat.parse.display
{
   import ghostcat.parse.graphics.GraphicsFill;
   import ghostcat.parse.graphics.GraphicsLineStyle;
   import ghostcat.parse.graphics.GraphicsRect;
   import ghostcat.parse.graphics.IGraphicsFill;
   import ghostcat.parse.graphics.IGraphicsLineStyle;
   
   public class SimpleRectParse extends RectParse
   {
      
      public function SimpleRectParse(param1:Number, param2:Number, param3:Number = 0, param4:Number = 16777215)
      {
         super(new GraphicsRect(0,0,param1,param2),isNaN(param3) ? null : new GraphicsLineStyle(0,param3),isNaN(param4) ? null : new GraphicsFill(param4));
      }
   }
}

