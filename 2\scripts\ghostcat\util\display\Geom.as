package ghostcat.util.display
{
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Stage;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import ghostcat.ui.UIConst;
   
   public final class Geom
   {
      
      public function Geom()
      {
         super();
      }
      
      public static function scaleToFit(param1:DisplayObject, param2:*, param3:String = "uniform") : void
      {
         if(!(<PERSON><PERSON><PERSON>(param1.width) && <PERSON><PERSON><PERSON>(param1.height)))
         {
            return;
         }
         var _loc4_:Rectangle = getRect(param2);
         param1.scaleX *= _loc4_.width / param1.width;
         param1.scaleY *= _loc4_.height / param1.height;
         if(param3 == UIConst.UNIFORM)
         {
            param1.scaleX = param1.scaleY = Math.min(param1.scaleX,param1.scaleY);
         }
         else if(param3 == UIConst.CROP)
         {
            param1.scaleX = param1.scaleY = Math.max(param1.scaleX,param1.scaleY);
         }
         var _loc5_:Rectangle = param1.getRect(param1.parent);
         param1.x += _loc4_.x - _loc5_.x;
         param1.y += _loc4_.y - _loc5_.y;
      }
      
      public static function zoom(param1:DisplayObject, param2:Number) : void
      {
         param1.x -= param1.width * (param2 - param1.scaleX) / 2;
         param1.y -= param1.height * (param2 - param1.scaleY) / 2;
         param1.scaleX = param1.scaleY = param2;
      }
      
      public static function getRect(param1:*, param2:DisplayObject = null) : Rectangle
      {
         var _loc3_:Rectangle = null;
         var _loc4_:Point = null;
         if(!param1)
         {
            return null;
         }
         if(param1 is Rectangle)
         {
            return (param1 as Rectangle).clone();
         }
         if(param1 is Stage)
         {
            _loc3_ = new Rectangle(0,0,(param1 as Stage).stageWidth,(param1 as Stage).stageHeight);
            if(param2)
            {
               return localRectToContent(_loc3_,param1 as DisplayObject,param2);
            }
            return _loc3_;
         }
         if(param1 is DisplayObject)
         {
            if(!param2)
            {
               param2 = (param1 as DisplayObject).parent;
            }
            if(param1.width == 0 || param1.height == 0)
            {
               _loc4_ = localToContent(new Point(),param1,param2);
               return new Rectangle(_loc4_.x,_loc4_.y,0,0);
            }
            if((param1 as DisplayObject).scrollRect)
            {
               return localRectToContent((param1 as DisplayObject).scrollRect,param1,param2);
            }
            return param1.getRect(param2);
         }
         return null;
      }
      
      public static function getRegPoint(param1:DisplayObject, param2:DisplayObject = null) : Point
      {
         if(!param2)
         {
            param2 = param1;
         }
         return param1.getRect(param2).topLeft;
      }
      
      public static function localToContent(param1:Point, param2:DisplayObject, param3:DisplayObject) : Point
      {
         if(Boolean(param3) && Boolean(param2))
         {
            return param3.globalToLocal(param2.localToGlobal(param1));
         }
         if(param2)
         {
            return param2.localToGlobal(param1);
         }
         if(param3)
         {
            return param3.globalToLocal(param1);
         }
         return null;
      }
      
      public static function localRectToContent(param1:Rectangle, param2:DisplayObject, param3:DisplayObject) : Rectangle
      {
         if(param2 == param3)
         {
            return param1;
         }
         var _loc4_:Point = localToContent(param1.topLeft,param2,param3);
         var _loc5_:Point = localToContent(param1.bottomRight,param2,param3);
         return new Rectangle(_loc4_.x,_loc4_.y,_loc5_.x - _loc4_.x,_loc5_.y - _loc4_.y);
      }
      
      public static function createCenterRect(param1:Point, param2:Number, param3:Number) : Rectangle
      {
         return new Rectangle(param1.x - param2 / 2,param1.y - param3 / 2,param2,param3);
      }
      
      public static function center(param1:*, param2:DisplayObject = null) : Point
      {
         var _loc3_:Rectangle = getRect(param1,param2);
         return _loc3_ ? new Point(_loc3_.x + _loc3_.width / 2,_loc3_.y + _loc3_.height / 2) : null;
      }
      
      public static function centerAtZero(param1:DisplayObject) : void
      {
         var _loc2_:Rectangle = Geom.getRect(param1);
         var _loc3_:Point = new Point(param1.x - _loc2_.x,param1.y - _loc2_.y);
         param1.x = -_loc2_.width / 2 + _loc3_.x;
         param1.y = -_loc2_.height / 2 + _loc3_.y;
      }
      
      public static function forceRectInside(param1:*, param2:*) : Boolean
      {
         var _loc3_:Rectangle = getRect(param1);
         var _loc4_:Rectangle = getRect(param2,param1 is DisplayObject ? param1.parent : param2);
         var _loc5_:Point = _loc3_.topLeft;
         var _loc6_:Boolean = false;
         if(_loc3_.right > _loc4_.right)
         {
            _loc5_.x = _loc4_.right - _loc3_.width;
            _loc6_ = true;
         }
         if(_loc3_.x < _loc4_.x)
         {
            _loc5_.x = _loc4_.x;
            _loc6_ = true;
         }
         if(_loc3_.bottom > _loc4_.bottom)
         {
            _loc5_.y = _loc4_.bottom - _loc3_.height;
            _loc6_ = true;
         }
         if(_loc3_.y < _loc4_.y)
         {
            _loc5_.y = _loc4_.y;
            _loc6_ = true;
         }
         moveTopLeftTo(param1,_loc5_);
         return _loc6_;
      }
      
      public static function forcePointInside(param1:*, param2:*) : Boolean
      {
         var _loc3_:Rectangle = getRect(param2,param1 is DisplayObject ? param1.parent : param2);
         var _loc4_:Boolean = false;
         if(param1.x > _loc3_.right)
         {
            param1.x = _loc3_.right;
            _loc4_ = true;
         }
         if(param1.x < _loc3_.x)
         {
            param1.x = _loc3_.x;
            _loc4_ = true;
         }
         if(param1.y > _loc3_.bottom)
         {
            param1.y = _loc3_.bottom;
            _loc4_ = true;
         }
         if(param1.y < _loc3_.y)
         {
            param1.y = _loc3_.y;
            _loc4_ = true;
         }
         return _loc4_;
      }
      
      public static function scalePoint(param1:Point, param2:Number) : Point
      {
         param1.x *= param2;
         param1.y *= param2;
         return param1;
      }
      
      public static function scaleByCenter(param1:*, param2:Number) : void
      {
         var _loc3_:Rectangle = getRect(param1);
         param1.width *= param2;
         param1.height *= param2;
         param1.x -= (param1.width - _loc3_.width) / 2;
         param1.y -= (param1.height - _loc3_.height) / 2;
      }
      
      public static function moveCenterTo(param1:*, param2:Point) : void
      {
         var _loc3_:Point = center(param1,param1.parent);
         param1.x += param2.x - _loc3_.x;
         param1.y += param2.y - _loc3_.y;
      }
      
      public static function moveTopLeftTo(param1:*, param2:Point, param3:DisplayObjectContainer = null) : void
      {
         if(!param3 && param1 is DisplayObject)
         {
            param3 = param1.parent;
         }
         var _loc4_:Point = getRect(param1,param3).topLeft;
         param1.x += param2.x - _loc4_.x;
         param1.y += param2.y - _loc4_.y;
      }
      
      public static function centerIn(param1:*, param2:*, param3:DisplayObjectContainer = null) : void
      {
         if(!param3 && param1 is DisplayObject)
         {
            param3 = param1.parent;
         }
         moveCenterTo(param1,center(param2,param3));
      }
      
      public static function copyPosition(param1:*, param2:*) : void
      {
         param2.x = param1.x;
         param2.y = param1.y;
      }
      
      public static function unionPoint(param1:*, param2:Number = NaN, param3:Number = NaN) : void
      {
         var _loc4_:Rectangle = getRect(param1);
         if(!isNaN(param2))
         {
            if(param2 < _loc4_.x)
            {
               param1.x = param2;
               param1.width = _loc4_.right - param2;
            }
            else if(param2 > _loc4_.right)
            {
               param1.width = param2 - _loc4_.x;
            }
         }
         if(!isNaN(param3))
         {
            if(param3 < _loc4_.y)
            {
               param1.y = param3;
               param1.height = _loc4_.bottom - param3;
            }
            else if(param3 > _loc4_.bottom)
            {
               param1.height = param3 - _loc4_.y;
            }
         }
      }
      
      public static function getRelativeLocation(param1:*, param2:*) : int
      {
         var _loc3_:Rectangle = getRect(param1);
         var _loc4_:Rectangle = getRect(param2);
         return (_loc3_.right <= _loc4_.left ? 0 : (_loc3_.left >= _loc4_.right ? 2 : 1)) + (_loc3_.bottom <= _loc4_.top ? 0 : (_loc3_.top >= _loc4_.bottom ? 6 : 3));
      }
      
      public static function getRelativeLocation2(param1:*, param2:*) : int
      {
         var _loc3_:Rectangle = getRect(param1);
         var _loc4_:Rectangle = getRect(param2);
         return (_loc3_.left < _loc4_.left ? 0 : (_loc3_.right > _loc4_.right ? 2 : 1)) + (_loc3_.top < _loc4_.top ? 0 : (_loc3_.bottom > _loc4_.bottom ? 6 : 3));
      }
   }
}

