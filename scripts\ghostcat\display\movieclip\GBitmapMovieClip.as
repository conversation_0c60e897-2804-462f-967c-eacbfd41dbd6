package ghostcat.display.movieclip
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Graphics;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import ghostcat.display.bitmap.BitmapMouseChecker;
   import ghostcat.display.bitmap.IBitmapDataDrawer;
   import ghostcat.parse.graphics.GraphicsBitmapFill;
   
   public class GBitmapMovieClip extends GMovieClipBase implements IBitmapDataDrawer
   {
      
      public var bitmaps:Array;
      
      public var disposeWhenDestory:Boolean = true;
      
      public var bitmapMouseChecker:BitmapMouseChecker;
      
      private var _enableMouseEvent:Boolean;
      
      public function GBitmapMovieClip(param1:Array = null, param2:Array = null, param3:Boolean = false)
      {
         if(!param1)
         {
            param1 = [];
         }
         super(new Bitmap(),true,param3);
         this.bitmaps = param1;
         this.labels = param2;
         if(Boolean(param1) && param1.length > 0)
         {
            (content as Bitmap).bitmapData = param1[0];
         }
         this.mouseEnabled = this.mouseChildren = false;
      }
      
      override protected function init() : void
      {
         this.bitmapMouseChecker = new BitmapMouseChecker(content as Bitmap);
         this.bitmapMouseChecker.enabled = this._enableMouseEvent;
         super.init();
      }
      
      public function get enableMouseEvent() : Boolean
      {
         return this._enableMouseEvent;
      }
      
      public function set enableMouseEvent(param1:Boolean) : void
      {
         this._enableMouseEvent = param1;
         if(this.bitmapMouseChecker)
         {
            this.bitmapMouseChecker.enabled = param1;
         }
      }
      
      public function linkBitmap(param1:Bitmap) : void
      {
         setContent(param1,false);
      }
      
      override public function set currentFrame(param1:int) : void
      {
         if(param1 < 1)
         {
            param1 = 1;
         }
         if(param1 > this.totalFrames)
         {
            param1 = this.totalFrames;
         }
         if(super.currentFrame == param1)
         {
            return;
         }
         super.currentFrame = param1;
         (content as Bitmap).bitmapData = this.bitmaps[param1 - 1];
      }
      
      override public function get totalFrames() : int
      {
         return this.bitmaps.length;
      }
      
      public function set labels(param1:Array) : void
      {
         _labels = param1;
      }
      
      public function dispose() : void
      {
         var _loc1_:BitmapData = null;
         for each(_loc1_ in this.bitmaps)
         {
            _loc1_.dispose();
         }
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         if(this.disposeWhenDestory)
         {
            this.dispose();
         }
         if(this.bitmapMouseChecker)
         {
            this.bitmapMouseChecker.destory();
         }
         super.destory();
      }
      
      public function createFromMovieClip(param1:MovieClip, param2:Rectangle = null, param3:int = 1, param4:int = -1, param5:Boolean = false) : void
      {
         var _loc6_:MovieClipCacher = new MovieClipCacher(param1,param2,param3,param4);
         if(param5)
         {
            _loc6_.result = this.bitmaps;
         }
         _loc6_.addEventListener(Event.COMPLETE,this.cacherCompleteHandler);
      }
      
      private function cacherCompleteHandler(param1:Event) : void
      {
         var _loc2_:MovieClipCacher = param1.currentTarget as MovieClipCacher;
         _loc2_.removeEventListener(Event.COMPLETE,this.cacherCompleteHandler);
         this.bitmaps = _loc2_.result;
         this.labels = _loc2_.mc.currentLabels;
         this.dispatchEvent(new Event(Event.COMPLETE));
      }
      
      public function drawToBitmapData(param1:BitmapData, param2:Point) : void
      {
         var _loc3_:BitmapData = (content as Bitmap).bitmapData;
         if(_loc3_)
         {
            param1.copyPixels(_loc3_,_loc3_.rect,position.add(param2));
         }
      }
      
      public function drawToShape(param1:Graphics, param2:Point) : void
      {
         var _loc3_:Point = new Point(x,y).add(param2);
         GraphicsBitmapFill.drawBitmpData(param1,(content as Bitmap).bitmapData,_loc3_.x,_loc3_.y);
      }
      
      public function getBitmapUnderMouse(param1:Number, param2:Number) : Array
      {
         var _loc3_:BitmapData = (content as Bitmap).bitmapData;
         return uint(_loc3_.getPixel32(param1 - x,param2 - y) >> 24) > 0 ? [this] : null;
      }
   }
}

