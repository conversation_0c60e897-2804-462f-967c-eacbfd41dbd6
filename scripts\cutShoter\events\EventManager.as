package cutShoter.events
{
   import flash.events.Event;
   
   public class EventManager extends Event
   {
      
      public static const SUBMITCOMPLETED:String = "submitCompleted";
      
      public static const COMMENTCOMPLETED:* = "commentcompleted";
      
      public static const COMMENTCANCLE:* = "commentcancle";
      
      public static const SAVING:String = "saving";
      
      public static const SAVECOMPLETED:String = "savecompleted";
      
      public function EventManager(param1:String, param2:Boolean = false, param3:<PERSON><PERSON><PERSON> = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         return new EventManager(type,bubbles,cancelable);
      }
      
      override public function toString() : String
      {
         return formatToString("EventManager","type","bubbles","cancelable","eventPhase");
      }
   }
}

