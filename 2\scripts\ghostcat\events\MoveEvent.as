package ghostcat.events
{
   import flash.events.Event;
   import flash.geom.Point;
   
   public class MoveEvent extends Event
   {
      
      public static const MOVE:String = "move";
      
      public var oldPosition:Point;
      
      public var newPosition:Point;
      
      public function MoveEvent(param1:String, param2:Boolean = false, param3:<PERSON>olean = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         var _loc1_:MoveEvent = new MoveEvent(type,bubbles,cancelable);
         _loc1_.oldPosition = this.oldPosition;
         _loc1_.newPosition = this.newPosition;
         return _loc1_;
      }
   }
}

