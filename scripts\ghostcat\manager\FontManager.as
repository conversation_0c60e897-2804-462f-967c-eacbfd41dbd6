package ghostcat.manager
{
   import flash.text.Font;
   import flash.text.FontStyle;
   import flash.text.TextField;
   import flash.text.TextFormat;
   import flash.utils.Dictionary;
   import ghostcat.util.Util;
   import ghostcat.util.core.Singleton;
   
   public class FontManager extends Singleton
   {
      
      private var textFormats:Dictionary = new Dictionary();
      
      public function FontManager()
      {
         super();
      }
      
      public static function get instance() : FontManager
      {
         return Singleton.getInstanceOrCreate(FontManager) as FontManager;
      }
      
      public function registerTextFormat(param1:String, param2:*) : void
      {
         var _loc3_:TextFormat = null;
         if(param2 is TextFormat)
         {
            _loc3_ = param2 as TextFormat;
         }
         else
         {
            _loc3_ = Util.createObject(TextFormat,param2);
         }
         this.textFormats[param1] = _loc3_;
      }
      
      public function getTextFormat(param1:String) : TextFormat
      {
         return this.textFormats[param1];
      }
      
      public function getFontByName(param1:String, param2:String = "regular", param3:Boolean = false) : Font
      {
         var _loc6_:Font = null;
         var _loc4_:Array = Font.enumerateFonts(param3);
         var _loc5_:int = 0;
         while(_loc5_ < _loc4_.length)
         {
            _loc6_ = _loc4_[_loc5_] as Font;
            if(_loc6_.fontName == param1 && _loc6_.fontStyle == param2)
            {
               return _loc6_;
            }
            _loc5_++;
         }
         return null;
      }
      
      public function getFontStyle(param1:TextFormat) : String
      {
         var _loc2_:Boolean = Boolean(param1.bold);
         var _loc3_:Boolean = Boolean(param1.italic);
         if(_loc2_ && _loc3_)
         {
            return FontStyle.BOLD_ITALIC;
         }
         if(_loc2_)
         {
            return FontStyle.BOLD;
         }
         if(_loc3_)
         {
            return FontStyle.ITALIC;
         }
         return FontStyle.REGULAR;
      }
      
      public function isEmbed(param1:String, param2:TextFormat) : Boolean
      {
         var _loc3_:Font = this.getFontByName(param2.font,this.getFontStyle(param2),false);
         return Boolean(_loc3_) && _loc3_.hasGlyphs(param1);
      }
      
      public function autoEmbedFonts(param1:TextField) : void
      {
         param1.embedFonts = this.isEmbed(param1.text,param1.defaultTextFormat);
      }
   }
}

