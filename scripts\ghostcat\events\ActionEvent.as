package ghostcat.events
{
   import flash.events.Event;
   
   public class ActionEvent extends Event
   {
      
      public static const ACTION:String = "action";
      
      public var action:String;
      
      public var parameters:Array;
      
      public function ActionEvent(param1:String, param2:Boolean = true, param3:Boolean = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         var _loc1_:ActionEvent = new ActionEvent(type,bubbles,cancelable);
         _loc1_.action = this.action;
         _loc1_.parameters = this.parameters;
         return _loc1_;
      }
   }
}

