package ghostcat.ui.controls
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.KeyboardEvent;
   import flash.events.TextEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.text.TextField;
   import flash.text.TextFieldAutoSize;
   import flash.text.TextFieldType;
   import flash.text.TextFormat;
   import ghostcat.display.GBase;
   import ghostcat.manager.FontManager;
   import ghostcat.parse.display.TextFieldParse;
   import ghostcat.text.TextFieldUtil;
   import ghostcat.text.TextUtil;
   import ghostcat.text.UBB;
   import ghostcat.ui.layout.Padding;
   import ghostcat.util.core.ClassFactory;
   import ghostcat.util.display.Geom;
   import ghostcat.util.display.SearchUtil;
   
   public class GText extends GBase
   {
      
      public static var defaultTextFormat:String;
      
      public static var textChangeHandler:Function;
      
      public static var fontFamilyReplacer:Object;
      
      public static var defaultSkin:ClassFactory = new ClassFactory(TextField,{"autoSize":TextFieldAutoSize.LEFT});
      
      public static var autoRebuildEmbedText:Boolean = false;
      
      private var _textFormat:String;
      
      private var _autoSize:String = "none";
      
      public var textField:TextField;
      
      public var textPadding:Padding;
      
      public var separateTextField:Boolean = false;
      
      public var enabledAdjustContextSize:Boolean = false;
      
      public var enabledLangage:Boolean = true;
      
      public var regExp:RegExp;
      
      public var changeWhenInput:Boolean = false;
      
      public var vertical:Boolean = false;
      
      public var ansiMaxChars:int;
      
      public var useHtml:Boolean = false;
      
      public var ubb:Boolean = false;
      
      public var autoSelect:Boolean;
      
      private var _editable:Boolean;
      
      protected var textBitmap:Bitmap;
      
      private var _asTextBitmap:Boolean;
      
      public function GText(param1:* = null, param2:Boolean = true, param3:Boolean = false, param4:Padding = null)
      {
         if(!param1)
         {
            param1 = defaultSkin;
         }
         if(param4)
         {
            this.textPadding = param4;
         }
         this.separateTextField = param3;
         super(param1,param2);
      }
      
      public function get autoSize() : String
      {
         return this.textField ? this.textField.autoSize : this._autoSize;
      }
      
      public function set autoSize(param1:String) : void
      {
         this._autoSize = param1;
         if(this.textField)
         {
            this.textField.autoSize = param1;
         }
      }
      
      public function get enabledAdjustTextSize() : Boolean
      {
         return this.autoSize != null;
      }
      
      public function set enabledAdjustTextSize(param1:Boolean) : void
      {
         this.autoSize = param1 ? TextFieldAutoSize.LEFT : null;
      }
      
      public function get textFormat() : String
      {
         return this._textFormat;
      }
      
      public function set textFormat(param1:String) : void
      {
         var _loc2_:TextFormat = null;
         this._textFormat = param1;
         if(Boolean(this.textField) && Boolean(param1))
         {
            _loc2_ = FontManager.instance.getTextFormat(param1);
            if(_loc2_)
            {
               this.applyTextFormat(_loc2_,true);
            }
         }
      }
      
      public function applyTextFormat(param1:TextFormat = null, param2:Boolean = false) : void
      {
         if(!this.textField)
         {
            return;
         }
         if(!param1)
         {
            param1 = this.textField.defaultTextFormat;
         }
         if(this.textField.length > 0)
         {
            this.textField.setTextFormat(param1,0,this.textField.length);
         }
         if(param2)
         {
            this.textField.defaultTextFormat = param1;
         }
         if(this.asTextBitmap)
         {
            this.reRenderTextBitmap();
         }
      }
      
      public function setTextFormat(param1:TextFormat, param2:int = -1, param3:int = -1) : void
      {
         if(!this.textField)
         {
            return;
         }
         this.textField.setTextFormat(param1,param2,param3);
         if(this.asTextBitmap)
         {
            this.reRenderTextBitmap();
         }
      }
      
      public function get maxChars() : int
      {
         return this.textField ? this.textField.maxChars : 0;
      }
      
      public function set maxChars(param1:int) : void
      {
         if(this.textField)
         {
            this.textField.maxChars = param1;
         }
      }
      
      public function get editable() : Boolean
      {
         return this.textField.type == TextFieldType.INPUT;
      }
      
      public function set editable(param1:Boolean) : void
      {
         this.textField.type = param1 ? TextFieldType.INPUT : TextFieldType.DYNAMIC;
         this.textField.mouseEnabled = param1;
      }
      
      public function get multiline() : Boolean
      {
         return this.textField.multiline;
      }
      
      public function set multiline(param1:Boolean) : void
      {
         this.textField.multiline = param1;
      }
      
      override public function setContent(param1:*, param2:Boolean = true) : void
      {
         super.setContent(param1,param2);
         this.getTextFieldFromSkin(this.skin);
      }
      
      protected function getTextFieldFromSkin(param1:DisplayObject) : void
      {
         var _loc3_:Point = null;
         if(this.textField)
         {
            this.textField.removeEventListener(TextEvent.TEXT_INPUT,this.textInputHandler);
            this.textField.removeEventListener(FocusEvent.FOCUS_IN,this.textFocusInHandler);
            this.textField.removeEventListener(FocusEvent.FOCUS_OUT,this.textFocusOutHandler);
            this.textField.removeEventListener(KeyboardEvent.KEY_DOWN,this.textKeyDownHandler);
            if(this.textField.parent == this)
            {
               this.removeChild(this.textField);
            }
         }
         this.textField = SearchUtil.findChildByClass(param1,TextField) as TextField;
         if(!this.textField)
         {
            this.separateTextField = true;
            this.textField = TextFieldParse.createTextField();
            addChild(this.textField);
            if(this.textPadding)
            {
               this.textPadding.adjectRect(this.textField,this);
            }
         }
         else
         {
            if(autoRebuildEmbedText && this.textField.embedFonts)
            {
               this.rebuildTextField();
            }
            _loc3_ = new Point(this.textField.x,this.textField.y);
            Geom.localToContent(_loc3_,content,this);
            this.textField.x = _loc3_.x;
            this.textField.y = _loc3_.y;
            if(this.separateTextField)
            {
               addChild(this.textField);
            }
         }
         this.textFormat = this.textFormat ? this.textFormat : defaultTextFormat;
         var _loc2_:TextFormat = this.textField.defaultTextFormat;
         if(Boolean(fontFamilyReplacer) && Boolean(fontFamilyReplacer[_loc2_.font]))
         {
            _loc2_.font = fontFamilyReplacer[_loc2_.font];
            this.textField.defaultTextFormat = _loc2_;
         }
         if(this.text)
         {
            if(this.useHtml || this.text.indexOf("<html>") != -1)
            {
               this.textField.htmlText = this.text;
            }
            else
            {
               this.textField.text = this.text;
            }
         }
         else
         {
            this.text = this.textField.text;
         }
         this.textField.addEventListener(TextEvent.TEXT_INPUT,this.textInputHandler);
         this.textField.addEventListener(FocusEvent.FOCUS_IN,this.textFocusInHandler);
         this.textField.addEventListener(FocusEvent.FOCUS_OUT,this.textFocusOutHandler);
         this.textField.addEventListener(KeyboardEvent.KEY_DOWN,this.textKeyDownHandler);
         this.textField.mouseEnabled = false;
      }
      
      public function rebuildTextField() : void
      {
         this.textField = TextFieldUtil.clone(this.textField,true);
      }
      
      public function get text() : String
      {
         return data as String;
      }
      
      public function set text(param1:String) : void
      {
         this.data = param1;
      }
      
      public function get curText() : String
      {
         return this.textField ? this.textField.text : null;
      }
      
      public function set htmlText(param1:String) : void
      {
         this.data = "<html>" + param1 + "</html>";
      }
      
      public function adjustContextSize() : void
      {
         var _loc1_:Rectangle = null;
         if(content)
         {
            if(this.textPadding)
            {
               this.textPadding.invent().adjectRectBetween(content,this.textField);
            }
            else
            {
               _loc1_ = getBounds(this);
               content.width = this.textField.width - _loc1_.x;
               content.height = this.textField.height - _loc1_.y;
            }
         }
      }
      
      override public function set data(param1:*) : void
      {
         var _loc2_:String = null;
         if(param1 == super.data)
         {
            return;
         }
         if(!param1)
         {
            _loc2_ = "";
         }
         else
         {
            _loc2_ = param1.toString();
         }
         if(textChangeHandler != null && this.enabledLangage)
         {
            _loc2_ = textChangeHandler(_loc2_);
         }
         super.data = _loc2_;
         if(this.vertical)
         {
            _loc2_ = TextUtil.vertical(_loc2_);
         }
         if(this.textField)
         {
            if(this.useHtml || _loc2_.indexOf("<html>") != -1)
            {
               if(this.ubb)
               {
                  _loc2_ = UBB.decode(_loc2_);
               }
               this.textField.htmlText = _loc2_;
            }
            else
            {
               this.textField.text = _loc2_;
            }
         }
         if(this.enabledAdjustContextSize)
         {
            this.adjustContextSize();
         }
         if(this.asTextBitmap)
         {
            this.reRenderTextBitmap();
         }
         dispatchEvent(new Event(Event.CHANGE));
      }
      
      public function set asTextBitmap(param1:Boolean) : void
      {
         this._asTextBitmap = param1;
         if(param1)
         {
            this.textField.visible = false;
            this.reRenderTextBitmap();
         }
         else
         {
            this.textField.visible = true;
            if(this.textBitmap)
            {
               this.textBitmap.bitmapData.dispose();
               this.textBitmap.parent.removeChild(this.textBitmap);
               this.textBitmap = null;
            }
         }
      }
      
      public function get asTextBitmap() : Boolean
      {
         return this._asTextBitmap;
      }
      
      public function reRenderTextBitmap() : void
      {
         if(this.textBitmap)
         {
            this.textBitmap.parent.removeChild(this.textBitmap);
            this.textBitmap.bitmapData.dispose();
         }
         this.textBitmap = new Bitmap(new BitmapData(Math.ceil(this.textField.width),Math.ceil(this.textField.height),true,0));
         this.textBitmap.x = this.textField.x;
         this.textBitmap.y = this.textField.y;
         this.textField.parent.addChild(this.textBitmap);
         this.textBitmap.bitmapData.draw(this.textField);
      }
      
      private function getANSILength(param1:String) : int
      {
         return TextUtil.getANSILength(param1);
      }
      
      protected function textInputHandler(param1:TextEvent) : void
      {
         if(Boolean(this.regExp) && !this.regExp.test(this.textField.text + param1.text))
         {
            param1.preventDefault();
         }
         if(Boolean(this.ansiMaxChars) && this.getANSILength(this.textField.text + param1.text) > this.ansiMaxChars)
         {
            param1.preventDefault();
         }
         if(this.changeWhenInput)
         {
            dispatchEvent(new Event(Event.CHANGE));
         }
      }
      
      protected function textFocusInHandler(param1:Event) : void
      {
         if(this.autoSelect)
         {
            this.textField.setSelection(0,this.textField.length);
         }
      }
      
      protected function textFocusOutHandler(param1:Event) : void
      {
         if(this.editable)
         {
            this.data = this.textField.text;
         }
      }
      
      protected function textKeyDownHandler(param1:KeyboardEvent) : void
      {
      }
      
      override protected function updateSize() : void
      {
         super.updateSize();
         if(this.textPadding)
         {
            this.textPadding.adjectRect(this.textField,this);
         }
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         if(this.textBitmap)
         {
            this.textBitmap.bitmapData.dispose();
         }
         if(this.textField)
         {
            this.textField.removeEventListener(TextEvent.TEXT_INPUT,this.textInputHandler);
            this.textField.removeEventListener(FocusEvent.FOCUS_IN,this.textFocusInHandler);
            this.textField.removeEventListener(FocusEvent.FOCUS_OUT,this.textFocusOutHandler);
            this.textField.removeEventListener(KeyboardEvent.KEY_DOWN,this.textKeyDownHandler);
            if(this.textField.parent == this)
            {
               removeChild(this.textField);
            }
         }
         super.destory();
      }
   }
}

