package ghostcat.text
{
   import flash.utils.ByteArray;
   
   public final class TextUtil
   {
      
      public function TextUtil()
      {
         super();
      }
      
      public static function right(param1:String, param2:int) : String
      {
         return param1.slice(param1.length - param2);
      }
      
      public static function subHtmlStr(param1:String, param2:Number = 0, param3:Number = 2147483647) : String
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Array = null;
         if(/<.*?>/.test(param1))
         {
            _loc4_ = param2;
            _loc5_ = 0;
            while(_loc5_ < param3 && _loc4_ < param1.length)
            {
               _loc6_ = param1.substr(_loc4_).match(/^<([\/\w]+).*?>/);
               if(_loc6_ != null)
               {
                  _loc4_ += _loc6_[0].length;
               }
               else
               {
                  _loc4_++;
                  _loc5_++;
               }
            }
            return param1.substr(param2,_loc4_);
         }
         return param1.substr(param2,param3);
      }
      
      public static function removeHTMLTag(param1:String) : String
      {
         return param1.replace(/<.*?>/g,"");
      }
      
      public static function removeR(param1:String) : String
      {
         return param1.replace(/\r/g,"");
      }
      
      public static function removeBR(param1:String) : String
      {
         return param1.replace(/\r|\n|<br>/g,"");
      }
      
      public static function vertical(param1:String) : String
      {
         var _loc2_:* = "";
         var _loc3_:int = 0;
         while(_loc3_ < param1.length)
         {
            _loc2_ += param1.charAt(_loc3_);
            if(_loc3_ < param1.length - 1)
            {
               _loc2_ += "\r";
            }
            _loc3_++;
         }
         return _loc2_;
      }
      
      public static function getANSILength(param1:String) : int
      {
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.writeMultiByte(param1,"gb2312");
         return _loc2_.length;
      }
   }
}

