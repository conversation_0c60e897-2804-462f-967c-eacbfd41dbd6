package cutShoter.view
{
   import cutShoter.events.EventManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol108")]
   public class mc_submiting extends MovieClip
   {
      
      public var mc_progress:MovieClip;
      
      public var btn_close:SimpleButton;
      
      public var title:MovieClip;
      
      public var mc_content:MovieClip;
      
      private var _timer:Timer;
      
      private var _progressRate:Number = 1;
      
      public function mc_submiting()
      {
         super();
         if(!stage)
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         else
         {
            this.init();
         }
      }
      
      private function init(param1:Event = null) : void
      {
         if(param1)
         {
            this.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         this.btn_close.visible = false;
         this.btn_close.addEventListener(MouseEvent.CLICK,this.closeHandler);
         dispatchEvent(new EventManager(EventManager.SAVING));
      }
      
      private function closeHandler(param1:MouseEvent) : void
      {
         dispatchEvent(new EventManager(EventManager.SAVECOMPLETED));
         if(this.parent)
         {
            this.parent.removeChild(this);
         }
      }
      
      public function startProgress(param1:Number) : void
      {
         this._timer = new Timer(param1);
         this._timer.addEventListener(TimerEvent.TIMER,this.timerHandler);
         this._timer.start();
      }
      
      private function timerHandler(param1:TimerEvent) : void
      {
         this.progressRate(this._progressRate);
         if(this._progressRate == 100)
         {
            dispatchEvent(new EventManager(EventManager.SAVECOMPLETED));
            this._timer.removeEventListener(TimerEvent.TIMER,this.timerHandler);
            if(this.parent)
            {
               this.parent.removeChild(this);
            }
         }
         ++this._progressRate;
      }
      
      public function progressRate(param1:Number) : void
      {
         this.mc_progress.gotoAndStop(param1);
      }
   }
}

