package ghostcat.ui.layout
{
   import flash.display.DisplayObject;
   import flash.geom.Rectangle;
   import ghostcat.util.display.Geom;
   
   public class Padding
   {
      
      public var left:Number;
      
      public var right:Number;
      
      public var top:Number;
      
      public var bottom:Number;
      
      public function Padding(param1:Number = NaN, param2:Number = NaN, param3:Number = NaN, param4:Number = NaN)
      {
         super();
         this.left = param1;
         this.right = param3;
         this.top = param2;
         this.bottom = param4;
      }
      
      public function adjectRect(param1:*, param2:*) : void
      {
         param2 = Geom.getRect(param2,param2);
         if(!isNaN(this.left))
         {
            param1.x = param2.x + this.left;
         }
         if(!isNaN(this.top))
         {
            param1.y = param2.y + this.top;
         }
         if(!isNaN(this.right))
         {
            param1.width = param2.right - this.right - param1.x;
         }
         if(!isNaN(this.bottom))
         {
            param1.height = param2.bottom - this.bottom - param1.y;
         }
      }
      
      public function adjectRectBetween(param1:*, param2:*) : void
      {
         var _loc4_:Rectangle = null;
         if(!isNaN(this.left))
         {
            param1.x = param2.x + this.left;
         }
         if(!isNaN(this.top))
         {
            param1.y = param2.y + this.top;
         }
         if(!isNaN(this.right))
         {
            param1.width = param2.x + param2.width - this.right - param1.x;
         }
         if(!isNaN(this.bottom))
         {
            param1.height = param2.y + param2.height - this.bottom - param1.y;
         }
         var _loc3_:DisplayObject = param1 as DisplayObject;
         if(_loc3_)
         {
            _loc4_ = Geom.getRect(_loc3_);
            _loc3_.x -= _loc4_.x - _loc3_.x;
            _loc3_.y -= _loc4_.y - _loc3_.y;
         }
      }
      
      public function invent() : Padding
      {
         return new Padding(-this.left,-this.top,-this.right,-this.bottom);
      }
      
      public function clone() : Padding
      {
         return new Padding(this.left,this.top,this.right,this.bottom);
      }
   }
}

