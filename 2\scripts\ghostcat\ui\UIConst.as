package ghostcat.ui
{
   public class UIConst
   {
      
      public static const UP:String = "up";
      
      public static const DOWN:String = "down";
      
      public static const TOP:String = "top";
      
      public static const BOTTOM:String = "bottom";
      
      public static const LEFT:String = "left";
      
      public static const RIGHT:String = "right";
      
      public static const CENTER:String = "center";
      
      public static const MIDDLE:String = "middle";
      
      public static const TOP_CENTER:String = "top_center";
      
      public static const BOTTOM_CENTER:String = "bottom_center";
      
      public static const LEFT_MIDDLE:String = "left_middle";
      
      public static const RIGHT_MIDDLE:String = "right_middle";
      
      public static const HORIZONTAL:String = "horizontal";
      
      public static const VERTICAL:String = "vertical";
      
      public static const TILE:String = "tile";
      
      public static const AUTO:String = "auto";
      
      public static const ON:String = "on";
      
      public static const OFF:String = "off";
      
      public static const UNIFORM:String = "uniform";
      
      public static const CROP:String = "crop";
      
      public static const FILL:String = "fill";
      
      public function UIConst()
      {
         super();
      }
   }
}

