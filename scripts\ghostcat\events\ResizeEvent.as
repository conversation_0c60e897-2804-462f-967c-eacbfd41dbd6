package ghostcat.events
{
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.geom.Point;
   
   public class ResizeEvent extends Event
   {
      
      public static const RESIZE:String = "resize";
      
      public static const CHILD_RESIZE:String = "child_resize";
      
      public var oldSize:Point;
      
      public var size:Point;
      
      public var child:DisplayObject;
      
      public function ResizeEvent(param1:String, param2:Boolean = false, param3:Boolean = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         var _loc1_:ResizeEvent = new ResizeEvent(type,bubbles,cancelable);
         _loc1_.oldSize = this.oldSize;
         _loc1_.size = this.size;
         _loc1_.child = this.child;
         return _loc1_;
      }
   }
}

