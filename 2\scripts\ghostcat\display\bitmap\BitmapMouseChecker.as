package ghostcat.display.bitmap
{
   import flash.display.Bitmap;
   import flash.events.MouseEvent;
   import ghostcat.events.TickEvent;
   import ghostcat.util.Tick;
   
   public class BitmapMouseChecker
   {
      
      public var bitmap:Bitmap;
      
      public var mouseOver:<PERSON>olean = false;
      
      public var mouseDown:<PERSON>olean = false;
      
      private var _enabled:Boolean = false;
      
      private var _enabledTick:Boolean = false;
      
      public var maskHitArea:Boolean = true;
      
      public function BitmapMouseChecker(param1:Bitmap)
      {
         super();
         this.bitmap = param1;
         this.enabled = true;
      }
      
      public function isMouseOver() : Boolean
      {
         if(!this.bitmap.bitmapData)
         {
            return false;
         }
         try
         {
            if(this.maskHitArea)
            {
               return uint(this.bitmap.bitmapData.getPixel32(this.bitmap.mouseX,this.bitmap.mouseY) >> 24) > 0;
            }
            return this.bitmap.bitmapData.rect.contains(this.bitmap.mouseX,this.bitmap.mouseY);
         }
         catch(e:Error)
         {
         }
         return false;
      }
      
      public function get enabled() : <PERSON><PERSON><PERSON>
      {
         return this._enabled;
      }
      
      public function set enabled(param1:<PERSON><PERSON><PERSON>) : void
      {
         if(this._enabled == param1)
         {
            return;
         }
         this._enabled = param1;
         if(param1)
         {
            this.bitmap.stage.addEventListener(MouseEvent.MOUSE_DOWN,this.mouseDownHandler);
            this.bitmap.stage.addEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
            this.bitmap.stage.addEventListener(MouseEvent.CLICK,this.clickHandler);
         }
         else
         {
            this.bitmap.stage.removeEventListener(MouseEvent.MOUSE_DOWN,this.mouseDownHandler);
            this.bitmap.stage.removeEventListener(MouseEvent.MOUSE_UP,this.mouseUpHandler);
            this.bitmap.stage.removeEventListener(MouseEvent.CLICK,this.clickHandler);
         }
         this.enabledTick = param1;
      }
      
      public function get enabledTick() : Boolean
      {
         return this._enabledTick;
      }
      
      public function set enabledTick(param1:Boolean) : void
      {
         if(this._enabledTick == param1)
         {
            return;
         }
         this._enabledTick = param1;
         if(this._enabled && this._enabledTick)
         {
            Tick.instance.addEventListener(TickEvent.TICK,this.tickHandler);
         }
         else
         {
            Tick.instance.removeEventListener(TickEvent.TICK,this.tickHandler);
         }
      }
      
      private function tickHandler(param1:TickEvent) : void
      {
         var _loc2_:Boolean = this.isMouseOver();
         if(this.mouseOver != _loc2_)
         {
            this.mouseOver = _loc2_;
            if(_loc2_)
            {
               this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_OVER,true,false,this.bitmap.mouseX,this.bitmap.mouseY));
               this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.ROLL_OVER,false,false,this.bitmap.mouseX,this.bitmap.mouseY));
            }
            else
            {
               this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_OUT,true,false,this.bitmap.mouseX,this.bitmap.mouseY));
               this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.ROLL_OUT,false,false,this.bitmap.mouseX,this.bitmap.mouseY));
            }
         }
      }
      
      private function mouseDownHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget != param1.target)
         {
            return;
         }
         var _loc2_:Boolean = this.isMouseOver();
         if(_loc2_)
         {
            this.mouseDown = true;
            this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_DOWN,true,false,this.bitmap.mouseX,this.bitmap.mouseY,null,param1.ctrlKey,param1.altKey,param1.shiftKey,param1.buttonDown));
         }
      }
      
      private function mouseUpHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget != param1.target)
         {
            return;
         }
         this.mouseDown = false;
         var _loc2_:Boolean = this.isMouseOver();
         if(_loc2_)
         {
            this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.MOUSE_UP,true,false,this.bitmap.mouseX,this.bitmap.mouseY,null,param1.ctrlKey,param1.altKey,param1.shiftKey,param1.buttonDown));
         }
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         if(param1.currentTarget != param1.target)
         {
            return;
         }
         var _loc2_:Boolean = this.isMouseOver();
         if(_loc2_)
         {
            this.bitmap.dispatchEvent(new MouseEvent(MouseEvent.CLICK,true,false,this.bitmap.mouseX,this.bitmap.mouseY,null,param1.ctrlKey,param1.altKey,param1.shiftKey,param1.buttonDown));
         }
      }
      
      public function destory() : void
      {
         this.enabled = false;
      }
   }
}

