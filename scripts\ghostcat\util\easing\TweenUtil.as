package ghostcat.util.easing
{
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.EventDispatcher;
   import flash.filters.BlurFilter;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.media.SoundTransform;
   import ghostcat.events.TickEvent;
   import ghostcat.util.MathUtil;
   import ghostcat.util.Tick;
   import ghostcat.util.core.Handler;
   import ghostcat.util.display.ColorUtil;
   
   public class TweenUtil extends EventDispatcher
   {
      
      private static var effects:Array = [];
      
      public static var enabledRelativeValue:Boolean = true;
      
      public static var updateWithCurrentTime:Boolean = false;
      
      Tick.instance.addEventListener(TickEvent.TICK,tickHandler);
      
      public var invert:Boolean = false;
      
      public var started:Boolean = false;
      
      public var paused:Boolean = false;
      
      public var target:*;
      
      public var currentTime:int;
      
      public var duration:int;
      
      public var ease:Function;
      
      public var fromValues:Object;
      
      public var toValues:Object;
      
      public var onStart:Function;
      
      public var onUpdate:Function;
      
      public var onComplete:Function;
      
      public var renderOnStart:Boolean = true;
      
      public function TweenUtil(param1:Object, param2:int, param3:Object)
      {
         var _loc4_:String = null;
         var _loc5_:Object = null;
         this.target = this.target;
         this.fromValues = new Object();
         this.toValues = new Object();
         super();
         if(!param3)
         {
            param3 = new Object();
         }
         this.target = param1;
         this.duration = param2;
         for(_loc4_ in param3)
         {
            switch(_loc4_)
            {
               case "ease":
               case "invert":
               case "renderOnStart":
               case "onStart":
               case "onUpdate":
               case "onComplete":
                  this[_loc4_] = param3[_loc4_];
                  break;
               case "onStartHandler":
                  this.addEventListener(TweenEvent.TWEEN_START,param3[_loc4_],false,0,true);
                  break;
               case "onUpdateHandler":
                  this.addEventListener(TweenEvent.TWEEN_UPDATE,param3[_loc4_],false,0,true);
                  break;
               case "onCompleteHandler":
                  this.addEventListener(TweenEvent.TWEEN_END,param3[_loc4_],false,0,true);
                  break;
               case "delay":
                  this.currentTime = -param3[_loc4_];
                  break;
               case "volume":
               case "pan":
                  this.fromValues[_loc4_] = param1["soundTransform"][_loc4_];
                  this.toValues[_loc4_] = param3[_loc4_];
                  break;
               case "frame":
                  this.fromValues[_loc4_] = (param1 as MovieClip).currentFrame;
                  this.toValues[_loc4_] = param3[_loc4_];
                  break;
               case "tint":
               case "tint2":
                  this.fromValues[_loc4_] = (param1 as DisplayObject).transform.colorTransform.color & 0xFFFFFF;
                  this.toValues[_loc4_] = param3[_loc4_];
                  break;
               case "autoAlpha":
                  this.fromValues[_loc4_] = (param1 as DisplayObject).alpha;
                  this.toValues[_loc4_] = param3[_loc4_];
                  break;
               case "dynamicPoint":
               case "motionBlur":
                  this.fromValues[_loc4_] = new Point((param1 as DisplayObject).x,(param1 as DisplayObject).y);
                  this.toValues[_loc4_] = param3[_loc4_];
                  break;
               case "startAt":
                  break;
               default:
                  this.fromValues[_loc4_] = param1[_loc4_];
                  this.toValues[_loc4_] = param3[_loc4_];
            }
            if(enabledRelativeValue && this.toValues[_loc4_] is String)
            {
               this.toValues[_loc4_] = this.fromValues[_loc4_] + Number(this.toValues[_loc4_]);
            }
         }
         if(param3.hasOwnProperty("startAt"))
         {
            _loc5_ = param3["startAt"];
            for(_loc4_ in _loc5_)
            {
               this.fromValues[_loc4_] = _loc5_[_loc4_];
            }
         }
         if(this.ease == null)
         {
            this.ease = Linear.easeOut;
         }
         effects.push(this);
      }
      
      public static function callLater(param1:Function, param2:Array = null, param3:int = 0) : void
      {
         new TweenUtil({},param3,{"onComplete":new Handler(param1,param2).toFunction()});
      }
      
      public static function to(param1:Object, param2:int, param3:Object) : TweenUtil
      {
         return new TweenUtil(param1,param2,param3);
      }
      
      public static function from(param1:Object, param2:int, param3:Object) : TweenUtil
      {
         param3.invert = true;
         return new TweenUtil(param1,param2,param3);
      }
      
      private static function tickHandler(param1:TickEvent) : void
      {
         update(param1.interval);
      }
      
      public static function update(param1:int = 0) : void
      {
         var _loc3_:TweenUtil = null;
         var _loc2_:int = int(effects.length - 1);
         while(_loc2_ >= 0)
         {
            _loc3_ = effects[_loc2_] as TweenUtil;
            if(_loc3_)
            {
               _loc3_.update(param1);
            }
            _loc2_--;
         }
      }
      
      private static function calculateValue(param1:TweenUtil, param2:int, param3:String) : *
      {
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc8_:* = 0;
         var _loc9_:* = 0;
         var _loc10_:* = 0;
         var _loc11_:* = 0;
         var _loc12_:* = 0;
         var _loc13_:* = 0;
         var _loc14_:int = 0;
         var _loc15_:int = 0;
         var _loc16_:int = 0;
         var _loc4_:* = param1.fromValues[param3];
         var _loc5_:* = param1.toValues[param3];
         if(_loc4_ is Point)
         {
            return new Point(param1.ease(param2,_loc4_.x,_loc5_.x - _loc4_.x,param1.duration),param1.ease(param2,_loc4_.y,_loc5_.y - _loc4_.y,param1.duration));
         }
         if(_loc4_ is Rectangle)
         {
            return new Rectangle(param1.ease(param2,_loc4_.x,_loc5_.x - _loc4_.x,param1.duration),param1.ease(param2,_loc4_.y,_loc5_.y - _loc4_.y,param1.duration),param1.ease(param2,_loc4_.width,_loc5_.width - _loc4_.width,param1.duration),param1.ease(param2,_loc4_.height,_loc5_.height - _loc4_.height,param1.duration));
         }
         if(_loc4_ is Array)
         {
            _loc6_ = [];
            _loc7_ = 0;
            while(_loc7_ < _loc4_.length)
            {
               _loc6_.push(param1.ease(param2,_loc4_[_loc7_],_loc5_[_loc7_] - _loc4_[_loc7_],param1.duration));
               _loc7_++;
            }
            return _loc6_;
         }
         if(param3 == "tint" || param3 == "tint2")
         {
            _loc8_ = _loc4_ >> 16 & 0xFF;
            _loc9_ = _loc4_ >> 8 & 0xFF;
            _loc10_ = _loc4_ & 0xFF;
            _loc11_ = _loc5_ >> 16 & 0xFF;
            _loc12_ = _loc5_ >> 8 & 0xFF;
            _loc13_ = _loc5_ & 0xFF;
            _loc14_ = MathUtil.limitIn(param1.ease(param2,_loc8_,_loc11_ - _loc8_,param1.duration),0,255);
            _loc15_ = MathUtil.limitIn(param1.ease(param2,_loc9_,_loc12_ - _loc9_,param1.duration),0,255);
            _loc16_ = MathUtil.limitIn(param1.ease(param2,_loc10_,_loc13_ - _loc10_,param1.duration),0,255);
            return ColorUtil.RGB(_loc14_,_loc15_,_loc16_);
         }
         return param1.ease(param2,_loc4_,_loc5_ - _loc4_,param1.duration);
      }
      
      private static function updateValue(param1:*, param2:String, param3:*) : void
      {
         var _loc4_:DisplayObject = null;
         var _loc5_:SoundTransform = null;
         var _loc6_:Point = null;
         _loc4_ = param1 as DisplayObject;
         switch(param2)
         {
            case "volume":
            case "pan":
               _loc5_ = param1["soundTransform"];
               _loc5_[param2] = param3;
               param1["soundTransform"] = _loc5_;
               break;
            case "autoAlpha":
               _loc4_.alpha = param3;
               _loc4_.visible = param3 > 0;
               break;
            case "frame":
               (param1 as MovieClip).gotoAndStop(int(param3));
               break;
            case "tint":
               _loc4_.transform.colorTransform = ColorUtil.getColorTransform(param3 as uint);
               break;
            case "tint2":
               _loc4_.transform.colorTransform = ColorUtil.getColorTransform2(param3 as uint);
               break;
            case "dynamicPoint":
               _loc4_.x = param3.x;
               _loc4_.y = param3.y;
               break;
            case "motionBlur":
               _loc6_ = new Point(Math.abs(param3.x - _loc4_.x),Math.abs(param3.y - _loc4_.y));
               if(_loc6_.length > 0)
               {
                  _loc4_.filters = [new BlurFilter(_loc6_.x,_loc6_.y)];
               }
               else
               {
                  _loc4_.filters = [];
               }
               _loc4_.x = param3.x;
               _loc4_.y = param3.y;
               break;
            default:
               param1[param2] = param3;
         }
      }
      
      public static function getTween(param1:Object) : Array
      {
         var _loc3_:TweenUtil = null;
         var _loc2_:Array = [];
         for each(_loc3_ in effects)
         {
            if(_loc3_.target == param1)
            {
               _loc2_.push(_loc3_);
            }
         }
         return _loc2_;
      }
      
      public static function pauseTween(param1:Object, param2:Boolean = true) : void
      {
         var _loc3_:TweenUtil = null;
         for each(_loc3_ in effects)
         {
            if(_loc3_.target == param1)
            {
               _loc3_.paused = true;
            }
         }
         if(param2)
         {
            update();
         }
      }
      
      public static function continueTween(param1:Object) : void
      {
         var _loc2_:TweenUtil = null;
         for each(_loc2_ in effects)
         {
            if(_loc2_.target == param1)
            {
               _loc2_.paused = false;
            }
         }
      }
      
      public static function removeTween(param1:Object, param2:Boolean = true) : void
      {
         var _loc4_:TweenUtil = null;
         var _loc3_:int = int(effects.length - 1);
         while(_loc3_ >= 0)
         {
            _loc4_ = effects[_loc3_] as TweenUtil;
            if(_loc4_.target == param1)
            {
               if(param2)
               {
                  _loc4_.duration = 0;
               }
               else
               {
                  effects.splice(_loc3_,1);
               }
            }
            _loc3_--;
         }
         if(param2)
         {
            update();
         }
      }
      
      public static function removeAllTween(param1:Boolean = true) : void
      {
         var _loc2_:TweenUtil = null;
         if(param1)
         {
            for each(_loc2_ in effects)
            {
               _loc2_.duration = 0;
            }
            update();
         }
         else
         {
            effects = [];
         }
      }
      
      public function update(param1:int = 0) : void
      {
         var _loc2_:String = null;
         var _loc3_:int = 0;
         var _loc4_:* = undefined;
         if(this.invert && this.renderOnStart)
         {
            for(_loc2_ in this.toValues)
            {
               updateValue(this.target,_loc2_,this.toValues[_loc2_]);
            }
            this.renderOnStart = false;
         }
         if(this.paused)
         {
            return;
         }
         this.currentTime += param1;
         if(this.currentTime < 0)
         {
            return;
         }
         if(!this.started)
         {
            this.started = true;
            if(this.onStart != null)
            {
               this.onStart();
            }
            this.dispatchEvent(new TweenEvent(TweenEvent.TWEEN_START));
         }
         if(this.currentTime >= this.duration)
         {
            for(_loc2_ in this.toValues)
            {
               updateValue(this.target,_loc2_,this.invert ? this.fromValues[_loc2_] : this.toValues[_loc2_]);
            }
         }
         else
         {
            for(_loc2_ in this.toValues)
            {
               _loc3_ = this.invert ? this.duration - this.currentTime : this.currentTime;
               _loc4_ = calculateValue(this,_loc3_,_loc2_);
               updateValue(this.target,_loc2_,_loc4_);
            }
         }
         if(this.onUpdate != null)
         {
            if(updateWithCurrentTime)
            {
               this.onUpdate(this.currentTime / this.duration);
            }
            else
            {
               this.onUpdate();
            }
         }
         this.dispatchEvent(new TweenEvent(TweenEvent.TWEEN_UPDATE));
         if(this.currentTime >= this.duration)
         {
            if(this.toValues.hasOwnProperty("motionBlur"))
            {
               (this.target as DisplayObject).filters = [];
            }
            if(this.onComplete != null)
            {
               this.onComplete();
            }
            effects.splice(effects.indexOf(this),1);
            this.dispatchEvent(new TweenEvent(TweenEvent.TWEEN_END));
         }
      }
      
      public function remove(param1:Boolean = true) : void
      {
         var _loc2_:int = int(effects.indexOf(this));
         if(_loc2_ != -1)
         {
            effects.splice(effects.indexOf(this),1);
            if(param1)
            {
               this.update();
            }
         }
      }
   }
}

