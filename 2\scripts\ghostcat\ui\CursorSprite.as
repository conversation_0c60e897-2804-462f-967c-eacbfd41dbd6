package ghostcat.ui
{
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import flash.ui.Mouse;
   import ghostcat.display.GBase;
   import ghostcat.display.ICursorManagerClient;
   import ghostcat.skin.cursor.CursorGroup;
   import ghostcat.util.Util;
   import ghostcat.util.core.ClassFactory;
   import ghostcat.util.display.DisplayUtil;
   
   public class CursorSprite extends GBase
   {
      
      private static var _instance:CursorSprite;
      
      public static var defaultSkin:ClassFactory = new ClassFactory(CursorGroup);
      
      public static const CURSOR_ARROW:String = "arrow";
      
      public static const CURSOR_BUSY:String = "busy";
      
      public static const CURSOR_POINT:String = "point";
      
      public static const CURSOR_DRAG:String = "drag";
      
      public static const CURSOR_H_DRAG:String = "hDrag";
      
      public static const CURSOR_V_DRAG:String = "vDrag";
      
      public static const CURSOR_HV_DRAG:String = "hvDrag";
      
      public static const CURSOR_VH_DRAG:String = "vhDrag";
      
      public static const CURSOR_ROTATE_TOPLEFT:String = "rotateTopLeft";
      
      public static const CURSOR_ROTATE_TOPRIGHT:String = "rotateTopRight";
      
      public static const CURSOR_ROTATE_BOTTOMLEFT:String = "rotateBottomLeft";
      
      public static const CURSOR_ROTATE_BOTTOMRIGHT:String = "rotateBottomRight";
      
      public var cursors:Object;
      
      public var onlyWithClasses:Array;
      
      public var defaultCursor:DisplayObject = null;
      
      private var curCursor:*;
      
      private var target:DisplayObject;
      
      private var buttonDown:Boolean = false;
      
      private var _lock:Boolean = false;
      
      public function CursorSprite(param1:DisplayObjectContainer = null)
      {
         var _loc2_:int = 0;
         var _loc3_:DisplayObject = null;
         super(null);
         if(!param1)
         {
            param1 = defaultSkin.newInstance();
         }
         this.acceptContentPosition = false;
         if(!_instance)
         {
            _instance = this;
         }
         DisplayUtil.setMouseEnabled(this,false);
         this.cursors = new Object();
         while(_loc2_ < param1.numChildren)
         {
            _loc3_ = param1.getChildAt(_loc2_);
            this.cursors[_loc3_.name] = _loc3_["constructor"] as Class;
            _loc2_++;
         }
      }
      
      public static function get instance() : CursorSprite
      {
         return _instance;
      }
      
      override protected function init() : void
      {
         stage.addEventListener(MouseEvent.MOUSE_MOVE,this.mouseMoveHandler);
         stage.addEventListener(MouseEvent.MOUSE_DOWN,this.updateButtonDownHandler);
         stage.addEventListener(MouseEvent.MOUSE_UP,this.updateButtonDownHandler);
         stage.addEventListener(MouseEvent.MOUSE_OVER,this.mouseOverHandler);
         stage.addEventListener(MouseEvent.MOUSE_OUT,this.mouseOutHandler);
         stage.addEventListener(Event.ENTER_FRAME,this.enterFrameHandler);
         stage.addEventListener(Event.MOUSE_LEAVE,this.mouseLeaveHandler);
      }
      
      public function setCursor(param1:*) : void
      {
         this._lock = true;
         this.setCurrentCursorClass(param1);
      }
      
      public function removeCursor() : void
      {
         this._lock = false;
      }
      
      public function get lock() : Boolean
      {
         return this._lock;
      }
      
      public function set lock(param1:Boolean) : void
      {
         this._lock = param1;
      }
      
      private function mouseMoveHandler(param1:MouseEvent) : void
      {
         if(content)
         {
            this.x = parent.mouseX;
            this.y = parent.mouseY;
         }
         this.updateButtonDownHandler(param1);
      }
      
      private function mouseOverHandler(param1:MouseEvent) : void
      {
         this.target = param1.target as DisplayObject;
         this.updateButtonDownHandler(param1);
      }
      
      private function mouseOutHandler(param1:MouseEvent) : void
      {
         this.target = null;
         this.updateButtonDownHandler(param1);
      }
      
      private function updateButtonDownHandler(param1:MouseEvent) : void
      {
         this.buttonDown = param1.buttonDown;
      }
      
      private function enterFrameHandler(param1:Event) : void
      {
         var _loc2_:MovieClip = null;
         DisplayUtil.moveToHigh(this);
         if(!this._lock)
         {
            this.setCurrentCursorClass(this.findCursorClass(this.target));
         }
         if(content)
         {
            if(this.content is MovieClip)
            {
               _loc2_ = this.content as MovieClip;
               if(this.buttonDown)
               {
                  _loc2_.nextFrame();
               }
               else
               {
                  _loc2_.prevFrame();
               }
            }
         }
      }
      
      private function mouseLeaveHandler(param1:Event) : void
      {
         if(!this._lock)
         {
            setContent(null);
         }
      }
      
      private function setCurrentCursorClass(param1:*) : void
      {
         if(this.curCursor == param1)
         {
            return;
         }
         this.curCursor = param1;
         var _loc2_:* = param1;
         if(_loc2_ is String)
         {
            _loc2_ = this.cursors[param1];
         }
         if(_loc2_ is Class)
         {
            _loc2_ = new _loc2_();
         }
         if(!_loc2_)
         {
            _loc2_ = this.defaultCursor;
         }
         if(_loc2_)
         {
            setContent(_loc2_);
            Mouse.hide();
            this.x = parent.mouseX;
            this.y = parent.mouseY;
         }
         else
         {
            setContent(null);
            Mouse.show();
         }
      }
      
      private function findCursorClass(param1:DisplayObject) : *
      {
         var _loc3_:* = undefined;
         var _loc2_:DisplayObject = param1;
         while(Boolean(_loc2_) && _loc2_.parent != _loc2_)
         {
            if(_loc2_ is TextField && TextField(_loc2_).selectable)
            {
               return null;
            }
            if(_loc2_ is Sprite && Sprite(_loc2_).buttonMode == true)
            {
               return null;
            }
            if(_loc2_ is ICursorManagerClient)
            {
               _loc3_ = (_loc2_ as ICursorManagerClient).cursor;
               if(_loc3_ && (this.onlyWithClasses == null || Util.isIn(_loc2_,this.onlyWithClasses)))
               {
                  return _loc3_;
               }
            }
            _loc2_ = _loc2_.parent;
         }
         return null;
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         if(stage)
         {
            stage.removeEventListener(MouseEvent.MOUSE_MOVE,this.mouseMoveHandler);
            stage.removeEventListener(MouseEvent.MOUSE_DOWN,this.updateButtonDownHandler);
            stage.removeEventListener(MouseEvent.MOUSE_UP,this.updateButtonDownHandler);
            stage.removeEventListener(MouseEvent.MOUSE_OVER,this.mouseOverHandler);
            stage.removeEventListener(MouseEvent.MOUSE_OUT,this.mouseOutHandler);
            stage.removeEventListener(Event.ENTER_FRAME,this.enterFrameHandler);
            stage.removeEventListener(Event.MOUSE_LEAVE,this.mouseLeaveHandler);
         }
         super.destory();
      }
   }
}

