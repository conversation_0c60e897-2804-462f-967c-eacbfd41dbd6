package ghostcat.ui.controls
{
   import ghostcat.display.movieclip.GMovieClip;
   import ghostcat.skin.ButtonSkin;
   import ghostcat.ui.layout.Padding;
   import ghostcat.util.core.ClassFactory;
   
   public class GButton extends GButtonBase
   {
      
      public static var defaultSkin:ClassFactory = new ClassFactory(ButtonSkin);
      
      public function GButton(param1:* = null, param2:Boolean = true, param3:<PERSON><PERSON>an = false, param4:Padding = null)
      {
         if(!param1)
         {
            param1 = GButton.defaultSkin;
         }
         super(param1,param2,param3,param4);
      }
      
      override protected function createMovieClip() : void
      {
         movie = new GMovieClip(content,false);
      }
   }
}

