package ghostcat.util.display
{
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Loader;
   
   public final class SearchUtil
   {
      
      public function SearchUtil()
      {
         super();
      }
      
      public static function findChildByClass(param1:DisplayObject, param2:Class, param3:int = 2147483647) : DisplayObject
      {
         var _loc4_:DisplayObject = null;
         if(param1 is param2)
         {
            return param1;
         }
         if(param1 is DisplayObjectContainer && !(param1 is Loader) && param3 > 0)
         {
            _loc4_ = findChildByClassIns(param1 as DisplayObjectContainer,param2,param3 - 1);
            if(_loc4_)
            {
               return _loc4_;
            }
         }
         return null;
      }
      
      private static function findChildByClassIns(param1:DisplayObjectContainer, param2:Class, param3:int = 2147483647) : DisplayObject
      {
         var _loc4_:int = 0;
         var _loc5_:DisplayObject = null;
         var _loc6_:int = 0;
         var _loc7_:DisplayObject = null;
         if(param3 > 0)
         {
            _loc4_ = param1.numChildren;
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               _loc5_ = param1.getChildAt(_loc6_);
               if(_loc5_ is param2)
               {
                  return _loc5_;
               }
               _loc6_++;
            }
            _loc6_ = 0;
            while(_loc6_ < _loc4_)
            {
               _loc5_ = param1.getChildAt(_loc6_);
               if(_loc5_ is DisplayObjectContainer && !(_loc5_ is Loader))
               {
                  _loc7_ = findChildByClassIns(_loc5_ as DisplayObjectContainer,param2,param3 - 1);
                  if(_loc7_)
                  {
                     return _loc7_;
                  }
               }
               _loc6_++;
            }
         }
         return null;
      }
      
      public static function findChildrenByClass(param1:DisplayObject, param2:Class, param3:int = 2147483647, param4:Array = null) : Array
      {
         if(param4 == null)
         {
            param4 = [];
         }
         if(param1 is param2)
         {
            param4.push(param1);
         }
         if(param1 is DisplayObjectContainer && !(param1 is Loader) && param3 > 0)
         {
            findChildrenByClassIns(param1 as DisplayObjectContainer,param2,param3 - 1,param4);
         }
         return param4;
      }
      
      private static function findChildrenByClassIns(param1:DisplayObjectContainer, param2:Class, param3:int = 2147483647, param4:Array = null) : void
      {
         var _loc5_:int = 0;
         var _loc6_:DisplayObject = null;
         var _loc7_:int = 0;
         if(param3 > 0)
         {
            _loc5_ = param1.numChildren;
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc6_ = param1.getChildAt(_loc7_);
               if(_loc6_ is param2)
               {
                  param4.push(_loc6_);
               }
               _loc7_++;
            }
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc6_ = param1.getChildAt(_loc7_);
               if(_loc6_ is DisplayObjectContainer && !(_loc6_ is Loader))
               {
                  findChildrenByClassIns(_loc6_ as DisplayObjectContainer,param2,param3 - 1,param4);
               }
               _loc7_++;
            }
         }
      }
      
      public static function findChildByProperty(param1:DisplayObject, param2:String, param3:* = null, param4:int = 2147483647) : DisplayObject
      {
         var _loc5_:DisplayObject = null;
         if(param1 == null)
         {
            return null;
         }
         if(param1.hasOwnProperty(param2))
         {
            if(param3 && param1[param2] == param3 || param3 == null)
            {
               return param1;
            }
         }
         if(param1 is DisplayObjectContainer && !(param1 is Loader) && param4 > 0)
         {
            _loc5_ = findChildByPropertyIns(param1 as DisplayObjectContainer,param2,param3,param4 - 1);
            if(_loc5_)
            {
               return _loc5_;
            }
         }
         return null;
      }
      
      private static function findChildByPropertyIns(param1:DisplayObjectContainer, param2:String, param3:* = null, param4:int = 2147483647) : DisplayObject
      {
         var _loc5_:int = 0;
         var _loc6_:DisplayObject = null;
         var _loc7_:int = 0;
         var _loc8_:DisplayObject = null;
         if(param1 == null)
         {
            return null;
         }
         if(param4 > 0)
         {
            _loc5_ = param1.numChildren;
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc6_ = param1.getChildAt(_loc7_);
               if(_loc6_.hasOwnProperty(param2))
               {
                  if(param3 && _loc6_[param2] == param3 || param3 == null)
                  {
                     return _loc6_;
                  }
               }
               _loc7_++;
            }
            _loc7_ = 0;
            while(_loc7_ < _loc5_)
            {
               _loc6_ = param1.getChildAt(_loc7_);
               if(_loc6_ is DisplayObjectContainer && !(_loc6_ is Loader))
               {
                  _loc8_ = findChildByPropertyIns(_loc6_ as DisplayObjectContainer,param2,param3,param4 - 1);
                  if(_loc8_)
                  {
                     return _loc8_;
                  }
               }
               _loc7_++;
            }
         }
         return null;
      }
      
      public static function findParentByClass(param1:DisplayObject, param2:Class, param3:int = 2147483647) : DisplayObject
      {
         if(param1 is param2)
         {
            return param1;
         }
         if(param1.parent && param1.parent != param1 && param3 > 0)
         {
            return findParentByClass(param1.parent,param2,param3 - 1);
         }
         return null;
      }
      
      private static function findParentsByClass(param1:DisplayObject, param2:Class, param3:int = 2147483647, param4:Array = null) : Array
      {
         if(param4 == null)
         {
            param4 = [];
         }
         if(param1 is param2)
         {
            param4.push(param1);
         }
         if(param1.parent && param1.parent != param1 && param3 > 0)
         {
            findParentsByClass(param1.parent,param2,param3 - 1,param4);
         }
         return param4;
      }
      
      public static function findParentByProperty(param1:DisplayObject, param2:String, param3:* = null, param4:int = 2147483647) : DisplayObject
      {
         if(param1 == null)
         {
            return null;
         }
         if(param1.hasOwnProperty(param2))
         {
            if(param3 && param1[param2] == param3 || param3 == null)
            {
               return param1;
            }
         }
         if(param1.parent && param1.parent != param1 && param4 > 0)
         {
            return findParentByProperty(param1.parent,param2,param3,param4 - 1);
         }
         return null;
      }
      
      public static function setPropertyByChild(param1:DisplayObject, param2:String, param3:*, param4:int = 2147483647) : void
      {
         var _loc5_:DisplayObjectContainer = null;
         var _loc6_:int = 0;
         if(param1 == null)
         {
            return;
         }
         if(param1.hasOwnProperty(param2))
         {
            param1[param2] = param3;
         }
         if(param1 is DisplayObjectContainer && !(param1 is Loader) && param4 > 0)
         {
            _loc5_ = DisplayObjectContainer(param1);
            _loc6_ = 0;
            while(_loc6_ < _loc5_.numChildren)
            {
               setPropertyByChild(_loc5_.getChildAt(_loc6_),param2,param3,param4 - 1);
               _loc6_++;
            }
         }
      }
      
      public static function setPropertyByParent(param1:DisplayObject, param2:String, param3:*, param4:int = 2147483647) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(param1.hasOwnProperty(param2))
         {
            param1[param2] = param3;
         }
         if(param1.parent && param1.parent != param1 && param4 > 0)
         {
            setPropertyByParent(param1.parent,param2,param3,param4 - 1);
         }
      }
   }
}

