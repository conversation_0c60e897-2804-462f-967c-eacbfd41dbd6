package asfiles.encoding
{
   import flash.display.*;
   import flash.geom.*;
   import flash.utils.*;
   
   public class JPEGEncoder
   {
      
      private var ZigZag:Array = [0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63];
      
      private var YTable:Array = new Array(64);
      
      private var UVTable:Array = new Array(64);
      
      private var fdtbl_Y:Array = new Array(64);
      
      private var fdtbl_UV:Array = new Array(64);
      
      private var YDC_HT:Array;
      
      private var UVDC_HT:Array;
      
      private var YAC_HT:Array;
      
      private var UVAC_HT:Array;
      
      private var std_dc_luminance_nrcodes:Array = [0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0];
      
      private var std_dc_luminance_values:Array = [0,1,2,3,4,5,6,7,8,9,10,11];
      
      private var std_ac_luminance_nrcodes:Array = [0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125];
      
      private var std_ac_luminance_values:Array = [1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250];
      
      private var std_dc_chrominance_nrcodes:Array = [0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0];
      
      private var std_dc_chrominance_values:Array = [0,1,2,3,4,5,6,7,8,9,10,11];
      
      private var std_ac_chrominance_nrcodes:Array = [0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119];
      
      private var std_ac_chrominance_values:Array = [0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];
      
      private var bitcode:Array = new Array(65535);
      
      private var category:Array = new Array(65535);
      
      private var byteout:ByteArray;
      
      private var bytenew:int = 0;
      
      private var bytepos:int = 7;
      
      internal var DU:Array = new Array(64);
      
      private var YDU:Array = new Array(64);
      
      private var UDU:Array = new Array(64);
      
      private var VDU:Array = new Array(64);
      
      public function JPEGEncoder(param1:Number = 50)
      {
         super();
         if(param1 <= 0)
         {
            param1 = 1;
         }
         if(param1 > 100)
         {
            param1 = 100;
         }
         var _loc2_:int = 0;
         if(param1 < 50)
         {
            _loc2_ = int(5000 / param1);
         }
         else
         {
            _loc2_ = int(200 - param1 * 2);
         }
         this.initHuffmanTbl();
         this.initCategoryNumber();
         this.initQuantTables(_loc2_);
      }
      
      private function initQuantTables(param1:int) : void
      {
         var _loc9_:Number = NaN;
         var _loc10_:Number = NaN;
         var _loc11_:int = 0;
         var _loc2_:Array = [16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99];
         var _loc3_:int = 0;
         while(_loc3_ < 64)
         {
            _loc9_ = Math.floor((_loc2_[_loc3_] * param1 + 50) / 100);
            if(_loc9_ < 1)
            {
               _loc9_ = 1;
            }
            else if(_loc9_ > 255)
            {
               _loc9_ = 255;
            }
            this.YTable[this.ZigZag[_loc3_]] = _loc9_;
            _loc3_++;
         }
         var _loc4_:Array = [17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99];
         var _loc5_:int = 0;
         while(_loc5_ < 64)
         {
            _loc10_ = Math.floor((_loc4_[_loc5_] * param1 + 50) / 100);
            if(_loc10_ < 1)
            {
               _loc10_ = 1;
            }
            else if(_loc10_ > 255)
            {
               _loc10_ = 255;
            }
            this.UVTable[this.ZigZag[_loc5_]] = _loc10_;
            _loc5_++;
         }
         var _loc6_:Array = [1,1.387039845,1.306562965,1.175875602,1,0.785694958,0.5411961,0.275899379];
         var _loc7_:int = 0;
         var _loc8_:int = 0;
         while(_loc8_ < 8)
         {
            _loc11_ = 0;
            while(_loc11_ < 8)
            {
               this.fdtbl_Y[_loc7_] = 1 / (this.YTable[this.ZigZag[_loc7_]] * _loc6_[_loc8_] * _loc6_[_loc11_] * 8);
               this.fdtbl_UV[_loc7_] = 1 / (this.UVTable[this.ZigZag[_loc7_]] * _loc6_[_loc8_] * _loc6_[_loc11_] * 8);
               _loc7_++;
               _loc11_++;
            }
            _loc8_++;
         }
      }
      
      private function computeHuffmanTbl(param1:Array, param2:Array) : Array
      {
         var _loc7_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:Array = new Array();
         var _loc6_:int = 1;
         while(_loc6_ <= 16)
         {
            _loc7_ = 1;
            while(_loc7_ <= param1[_loc6_])
            {
               _loc5_[param2[_loc4_]] = new BitString();
               _loc5_[param2[_loc4_]].val = _loc3_;
               _loc5_[param2[_loc4_]].len = _loc6_;
               _loc4_++;
               _loc3_++;
               _loc7_++;
            }
            _loc3_ *= 2;
            _loc6_++;
         }
         return _loc5_;
      }
      
      private function initHuffmanTbl() : void
      {
         this.YDC_HT = this.computeHuffmanTbl(this.std_dc_luminance_nrcodes,this.std_dc_luminance_values);
         this.UVDC_HT = this.computeHuffmanTbl(this.std_dc_chrominance_nrcodes,this.std_dc_chrominance_values);
         this.YAC_HT = this.computeHuffmanTbl(this.std_ac_luminance_nrcodes,this.std_ac_luminance_values);
         this.UVAC_HT = this.computeHuffmanTbl(this.std_ac_chrominance_nrcodes,this.std_ac_chrominance_values);
      }
      
      private function initCategoryNumber() : *
      {
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc1_:* = 1;
         var _loc2_:* = 2;
         var _loc3_:int = 1;
         while(_loc3_ <= 15)
         {
            _loc4_ = _loc1_;
            while(_loc4_ < _loc2_)
            {
               this.category[32767 + _loc4_] = _loc3_;
               this.bitcode[32767 + _loc4_] = new BitString();
               this.bitcode[32767 + _loc4_].len = _loc3_;
               this.bitcode[32767 + _loc4_].val = _loc4_;
               _loc4_++;
            }
            _loc5_ = -(_loc2_ - 1);
            while(_loc5_ <= -_loc1_)
            {
               this.category[32767 + _loc5_] = _loc3_;
               this.bitcode[32767 + _loc5_] = new BitString();
               this.bitcode[32767 + _loc5_].len = _loc3_;
               this.bitcode[32767 + _loc5_].val = _loc2_ - 1 + _loc5_;
               _loc5_++;
            }
            _loc1_ <<= 1;
            _loc2_ <<= 1;
            _loc3_++;
         }
      }
      
      private function writeBits(param1:BitString) : void
      {
         var _loc2_:int = param1.val;
         var _loc3_:int = param1.len - 1;
         while(_loc3_ >= 0)
         {
            if(_loc2_ & uint(1 << _loc3_))
            {
               this.bytenew |= uint(1 << this.bytepos);
            }
            _loc3_--;
            --this.bytepos;
            if(this.bytepos < 0)
            {
               if(this.bytenew == 255)
               {
                  this.writeByte(255);
                  this.writeByte(0);
               }
               else
               {
                  this.writeByte(this.bytenew);
               }
               this.bytepos = 7;
               this.bytenew = 0;
            }
         }
      }
      
      private function writeByte(param1:int) : *
      {
         this.byteout.writeByte(param1);
      }
      
      private function writeWord(param1:int) : *
      {
         this.writeByte(param1 >> 8 & 0xFF);
         this.writeByte(param1 & 0xFF);
      }
      
      private function fDCTQuant(param1:Array, param2:Array) : Array
      {
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc10_:Number = NaN;
         var _loc11_:Number = NaN;
         var _loc12_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc14_:Number = NaN;
         var _loc15_:Number = NaN;
         var _loc16_:Number = NaN;
         var _loc17_:Number = NaN;
         var _loc18_:Number = NaN;
         var _loc19_:Number = NaN;
         var _loc20_:Number = NaN;
         var _loc21_:Number = NaN;
         var _loc22_:Number = NaN;
         var _loc23_:Number = NaN;
         var _loc24_:Number = NaN;
         var _loc25_:Number = NaN;
         var _loc26_:Number = NaN;
         var _loc27_:Number = NaN;
         var _loc28_:Number = NaN;
         var _loc29_:Number = NaN;
         var _loc30_:Number = NaN;
         var _loc31_:Number = NaN;
         var _loc32_:Number = NaN;
         var _loc33_:Number = NaN;
         var _loc34_:Number = NaN;
         var _loc35_:Number = NaN;
         var _loc36_:Number = NaN;
         var _loc37_:Number = NaN;
         var _loc38_:Number = NaN;
         var _loc39_:Number = NaN;
         var _loc40_:Number = NaN;
         var _loc41_:Number = NaN;
         var _loc42_:Number = NaN;
         var _loc43_:Number = NaN;
         var _loc44_:Number = NaN;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         while(_loc4_ < 8)
         {
            _loc7_ = param1[_loc3_ + 0] + param1[_loc3_ + 7];
            _loc8_ = param1[_loc3_ + 0] - param1[_loc3_ + 7];
            _loc9_ = param1[_loc3_ + 1] + param1[_loc3_ + 6];
            _loc10_ = param1[_loc3_ + 1] - param1[_loc3_ + 6];
            _loc11_ = param1[_loc3_ + 2] + param1[_loc3_ + 5];
            _loc12_ = param1[_loc3_ + 2] - param1[_loc3_ + 5];
            _loc13_ = param1[_loc3_ + 3] + param1[_loc3_ + 4];
            _loc14_ = param1[_loc3_ + 3] - param1[_loc3_ + 4];
            _loc15_ = _loc7_ + _loc13_;
            _loc16_ = _loc7_ - _loc13_;
            _loc17_ = _loc9_ + _loc11_;
            _loc18_ = _loc9_ - _loc11_;
            param1[_loc3_ + 0] = _loc15_ + _loc17_;
            param1[_loc3_ + 4] = _loc15_ - _loc17_;
            _loc19_ = (_loc18_ + _loc16_) * 0.707106781;
            param1[_loc3_ + 2] = _loc16_ + _loc19_;
            param1[_loc3_ + 6] = _loc16_ - _loc19_;
            _loc15_ = _loc14_ + _loc12_;
            _loc17_ = _loc12_ + _loc10_;
            _loc18_ = _loc10_ + _loc8_;
            _loc20_ = (_loc15_ - _loc18_) * 0.382683433;
            _loc21_ = 0.5411961 * _loc15_ + _loc20_;
            _loc22_ = 1.306562965 * _loc18_ + _loc20_;
            _loc23_ = _loc17_ * 0.707106781;
            _loc24_ = _loc8_ + _loc23_;
            _loc25_ = _loc8_ - _loc23_;
            param1[_loc3_ + 5] = _loc25_ + _loc21_;
            param1[_loc3_ + 3] = _loc25_ - _loc21_;
            param1[_loc3_ + 1] = _loc24_ + _loc22_;
            param1[_loc3_ + 7] = _loc24_ - _loc22_;
            _loc3_ += 8;
            _loc4_++;
         }
         _loc3_ = 0;
         var _loc5_:int = 0;
         while(_loc5_ < 8)
         {
            _loc26_ = param1[_loc3_ + 0] + param1[_loc3_ + 56];
            _loc27_ = param1[_loc3_ + 0] - param1[_loc3_ + 56];
            _loc28_ = param1[_loc3_ + 8] + param1[_loc3_ + 48];
            _loc29_ = param1[_loc3_ + 8] - param1[_loc3_ + 48];
            _loc30_ = param1[_loc3_ + 16] + param1[_loc3_ + 40];
            _loc31_ = param1[_loc3_ + 16] - param1[_loc3_ + 40];
            _loc32_ = param1[_loc3_ + 24] + param1[_loc3_ + 32];
            _loc33_ = param1[_loc3_ + 24] - param1[_loc3_ + 32];
            _loc34_ = _loc26_ + _loc32_;
            _loc35_ = _loc26_ - _loc32_;
            _loc36_ = _loc28_ + _loc30_;
            _loc37_ = _loc28_ - _loc30_;
            param1[_loc3_ + 0] = _loc34_ + _loc36_;
            param1[_loc3_ + 32] = _loc34_ - _loc36_;
            _loc38_ = (_loc37_ + _loc35_) * 0.707106781;
            param1[_loc3_ + 16] = _loc35_ + _loc38_;
            param1[_loc3_ + 48] = _loc35_ - _loc38_;
            _loc34_ = _loc33_ + _loc31_;
            _loc36_ = _loc31_ + _loc29_;
            _loc37_ = _loc29_ + _loc27_;
            _loc39_ = (_loc34_ - _loc37_) * 0.382683433;
            _loc40_ = 0.5411961 * _loc34_ + _loc39_;
            _loc41_ = 1.306562965 * _loc37_ + _loc39_;
            _loc42_ = _loc36_ * 0.707106781;
            _loc43_ = _loc27_ + _loc42_;
            _loc44_ = _loc27_ - _loc42_;
            param1[_loc3_ + 40] = _loc44_ + _loc40_;
            param1[_loc3_ + 24] = _loc44_ - _loc40_;
            param1[_loc3_ + 8] = _loc43_ + _loc41_;
            param1[_loc3_ + 56] = _loc43_ - _loc41_;
            _loc3_++;
            _loc5_++;
         }
         var _loc6_:int = 0;
         while(_loc6_ < 64)
         {
            param1[_loc6_] = Math.round(param1[_loc6_] * param2[_loc6_]);
            _loc6_++;
         }
         return param1;
      }
      
      private function writeAPP0() : void
      {
         this.writeWord(65504);
         this.writeWord(16);
         this.writeByte(74);
         this.writeByte(70);
         this.writeByte(73);
         this.writeByte(70);
         this.writeByte(0);
         this.writeByte(1);
         this.writeByte(1);
         this.writeByte(0);
         this.writeWord(1);
         this.writeWord(1);
         this.writeByte(0);
         this.writeByte(0);
      }
      
      private function writeSOF0(param1:int, param2:int) : void
      {
         this.writeWord(65472);
         this.writeWord(17);
         this.writeByte(8);
         this.writeWord(param2);
         this.writeWord(param1);
         this.writeByte(3);
         this.writeByte(1);
         this.writeByte(17);
         this.writeByte(0);
         this.writeByte(2);
         this.writeByte(17);
         this.writeByte(1);
         this.writeByte(3);
         this.writeByte(17);
         this.writeByte(1);
      }
      
      private function writeDQT() : void
      {
         this.writeWord(65499);
         this.writeWord(132);
         this.writeByte(0);
         var _loc1_:int = 0;
         while(_loc1_ < 64)
         {
            this.writeByte(this.YTable[_loc1_]);
            _loc1_++;
         }
         this.writeByte(1);
         var _loc2_:int = 0;
         while(_loc2_ < 64)
         {
            this.writeByte(this.UVTable[_loc2_]);
            _loc2_++;
         }
      }
      
      private function writeDHT() : void
      {
         this.writeWord(65476);
         this.writeWord(418);
         this.writeByte(0);
         var _loc1_:int = 0;
         while(_loc1_ < 16)
         {
            this.writeByte(this.std_dc_luminance_nrcodes[_loc1_ + 1]);
            _loc1_++;
         }
         var _loc2_:int = 0;
         while(_loc2_ <= 11)
         {
            this.writeByte(this.std_dc_luminance_values[_loc2_]);
            _loc2_++;
         }
         this.writeByte(16);
         var _loc3_:int = 0;
         while(_loc3_ < 16)
         {
            this.writeByte(this.std_ac_luminance_nrcodes[_loc3_ + 1]);
            _loc3_++;
         }
         var _loc4_:int = 0;
         while(_loc4_ <= 161)
         {
            this.writeByte(this.std_ac_luminance_values[_loc4_]);
            _loc4_++;
         }
         this.writeByte(1);
         var _loc5_:int = 0;
         while(_loc5_ < 16)
         {
            this.writeByte(this.std_dc_chrominance_nrcodes[_loc5_ + 1]);
            _loc5_++;
         }
         var _loc6_:int = 0;
         while(_loc6_ <= 11)
         {
            this.writeByte(this.std_dc_chrominance_values[_loc6_]);
            _loc6_++;
         }
         this.writeByte(17);
         var _loc7_:int = 0;
         while(_loc7_ < 16)
         {
            this.writeByte(this.std_ac_chrominance_nrcodes[_loc7_ + 1]);
            _loc7_++;
         }
         var _loc8_:int = 0;
         while(_loc8_ <= 161)
         {
            this.writeByte(this.std_ac_chrominance_values[_loc8_]);
            _loc8_++;
         }
      }
      
      private function writeSOS() : void
      {
         this.writeWord(65498);
         this.writeWord(12);
         this.writeByte(3);
         this.writeByte(1);
         this.writeByte(0);
         this.writeByte(2);
         this.writeByte(17);
         this.writeByte(3);
         this.writeByte(17);
         this.writeByte(0);
         this.writeByte(63);
         this.writeByte(0);
      }
      
      private function processDU(param1:Array, param2:Array, param3:Number, param4:Array, param5:Array) : Number
      {
         var _loc13_:int = 0;
         var _loc14_:int = 0;
         var _loc15_:int = 0;
         var _loc6_:BitString = param5[0];
         var _loc7_:BitString = param5[240];
         var _loc8_:Array = this.fDCTQuant(param1,param2);
         var _loc9_:int = 0;
         while(_loc9_ < 64)
         {
            this.DU[this.ZigZag[_loc9_]] = _loc8_[_loc9_];
            _loc9_++;
         }
         var _loc10_:int = this.DU[0] - param3;
         param3 = Number(this.DU[0]);
         if(_loc10_ == 0)
         {
            this.writeBits(param4[0]);
         }
         else
         {
            this.writeBits(param4[this.category[32767 + _loc10_]]);
            this.writeBits(this.bitcode[32767 + _loc10_]);
         }
         var _loc11_:int = 63;
         while(_loc11_ > 0 && this.DU[_loc11_] == 0)
         {
            _loc11_--;
         }
         if(_loc11_ == 0)
         {
            this.writeBits(_loc6_);
            return param3;
         }
         var _loc12_:int = 1;
         while(_loc12_ <= _loc11_)
         {
            _loc13_ = _loc12_;
            while(this.DU[_loc12_] == 0 && _loc12_ <= _loc11_)
            {
               _loc12_++;
            }
            _loc14_ = _loc12_ - _loc13_;
            if(_loc14_ >= 16)
            {
               _loc15_ = 1;
               while(_loc15_ <= _loc14_ / 16)
               {
                  this.writeBits(_loc7_);
                  _loc15_++;
               }
               _loc14_ = int(_loc14_ & 0x0F);
            }
            this.writeBits(param5[_loc14_ * 16 + this.category[32767 + this.DU[_loc12_]]]);
            this.writeBits(this.bitcode[32767 + this.DU[_loc12_]]);
            _loc12_++;
         }
         if(_loc11_ != 63)
         {
            this.writeBits(_loc6_);
         }
         return param3;
      }
      
      private function RGB2YUV(param1:BitmapData, param2:int, param3:int) : *
      {
         var _loc6_:int = 0;
         var _loc7_:uint = 0;
         var _loc8_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc10_:Number = NaN;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         while(_loc5_ < 8)
         {
            _loc6_ = 0;
            while(_loc6_ < 8)
            {
               _loc7_ = param1.getPixel32(param2 + _loc6_,param3 + _loc5_);
               _loc8_ = Number(_loc7_ >> 16 & 0xFF);
               _loc9_ = Number(_loc7_ >> 8 & 0xFF);
               _loc10_ = Number(_loc7_ & 0xFF);
               this.YDU[_loc4_] = 0.299 * _loc8_ + 0.587 * _loc9_ + 0.114 * _loc10_ - 128;
               this.UDU[_loc4_] = -0.16874 * _loc8_ + -0.33126 * _loc9_ + 0.5 * _loc10_;
               this.VDU[_loc4_] = 0.5 * _loc8_ + -0.41869 * _loc9_ + -0.08131 * _loc10_;
               _loc4_++;
               _loc6_++;
            }
            _loc5_++;
         }
      }
      
      public function encode(param1:BitmapData) : ByteArray
      {
         var _loc6_:int = 0;
         var _loc7_:BitString = null;
         this.byteout = new ByteArray();
         this.bytenew = 0;
         this.bytepos = 7;
         this.writeWord(65496);
         this.writeAPP0();
         this.writeDQT();
         this.writeSOF0(param1.width,param1.height);
         this.writeDHT();
         this.writeSOS();
         var _loc2_:Number = 0;
         var _loc3_:Number = 0;
         var _loc4_:Number = 0;
         this.bytenew = 0;
         this.bytepos = 7;
         var _loc5_:int = 0;
         while(_loc5_ < param1.height)
         {
            _loc6_ = 0;
            while(_loc6_ < param1.width)
            {
               this.RGB2YUV(param1,_loc6_,_loc5_);
               _loc2_ = this.processDU(this.YDU,this.fdtbl_Y,_loc2_,this.YDC_HT,this.YAC_HT);
               _loc3_ = this.processDU(this.UDU,this.fdtbl_UV,_loc3_,this.UVDC_HT,this.UVAC_HT);
               _loc4_ = this.processDU(this.VDU,this.fdtbl_UV,_loc4_,this.UVDC_HT,this.UVAC_HT);
               _loc6_ += 8;
            }
            _loc5_ += 8;
         }
         if(this.bytepos >= 0)
         {
            _loc7_ = new BitString();
            _loc7_.len = this.bytepos + 1;
            _loc7_.val = (1 << this.bytepos + 1) - 1;
            this.writeBits(_loc7_);
         }
         this.writeWord(65497);
         return this.byteout;
      }
   }
}

class BitString
{
   
   public var len:int = 0;
   
   public var val:int = 0;
   
   public function BitString()
   {
      super();
   }
}
