package ghostcat.util
{
   import flash.display.DisplayObjectContainer;
   import flash.utils.Dictionary;
   import flash.utils.describeType;
   import flash.utils.getDefinitionByName;
   import flash.utils.getQualifiedClassName;
   import ghostcat.debug.Debug;
   
   public class ReflectUtil
   {
      
      private static var describeTypeCache:Dictionary = new Dictionary(true);
      
      public function ReflectUtil()
      {
         super();
      }
      
      public static function getDescribeType(param1:*, param2:Boolean = true) : XML
      {
         var _loc3_:XML = null;
         param1 = getClass(param1);
         if(describeTypeCache.hasOwnProperty(param1))
         {
            _loc3_ = describeTypeCache[param1];
         }
         else
         {
            _loc3_ = describeType(param1);
            if(param2)
            {
               describeTypeCache[param1] = _loc3_;
            }
         }
         return _loc3_;
      }
      
      public static function clearDescribeTypeCache(param1:* = null) : void
      {
         if(param1)
         {
            delete describeTypeCache[param1];
         }
         else
         {
            describeTypeCache = new Dictionary(true);
         }
      }
      
      public static function getMethodList(param1:*) : Object
      {
         var _loc2_:XML = null;
         var _loc3_:XML = null;
         var _loc4_:String = null;
         var _loc5_:Object = null;
         _loc2_ = getDescribeType(param1);
         _loc5_ = new Object();
         for each(_loc3_ in _loc2_..method)
         {
            _loc4_ = <EMAIL>();
            _loc5_[_loc4_] = param1[_loc4_];
         }
         return _loc5_;
      }
      
      public static function getPropertyList(param1:*, param2:Boolean = false) : Object
      {
         var _loc3_:XML = null;
         var _loc4_:XML = null;
         var _loc5_:String = null;
         var _loc6_:Object = null;
         _loc3_ = getDescribeType(param1);
         _loc6_ = new Object();
         for each(_loc4_ in _loc3_..accessor)
         {
            if(!(_loc4_.@access == "readonly" && param2))
            {
               _loc5_ = <EMAIL>();
               if(param1.hasOwnProperty(_loc5_))
               {
                  _loc6_[_loc5_] = param1[_loc5_];
               }
            }
         }
         for each(_loc4_ in _loc3_..variable)
         {
            _loc5_ = <EMAIL>();
            _loc6_[_loc5_] = param1[_loc5_];
         }
         return _loc6_;
      }
      
      public static function getTypeByProperty(param1:*, param2:String) : Class
      {
         var xml:XML = null;
         var result:XMLList = null;
         var obj:* = param1;
         var property:String = param2;
         xml = getDescribeType(obj);
         result = xml..*.accessor.(@name == property);
         if(result.length() > 0)
         {
            return getDefinitionByName(result[0].@type) as Class;
         }
         result = xml..*.variable.(@name == property);
         if(result.length() > 0)
         {
            return getDefinitionByName(result[0].@type) as Class;
         }
         return null;
      }
      
      public static function getPropertyTypeList(param1:*, param2:Boolean = false) : Object
      {
         var _loc3_:XML = null;
         var _loc4_:XML = null;
         var _loc5_:String = null;
         var _loc6_:Object = null;
         _loc3_ = getDescribeType(param1);
         _loc6_ = new Object();
         for each(_loc4_ in _loc3_..accessor)
         {
            if(!(_loc4_.@access == "readonly" && param2))
            {
               _loc5_ = <EMAIL>();
               if(param1.hasOwnProperty(_loc5_))
               {
                  _loc6_[_loc5_] = getDefinitionByName(_loc4_.@type);
               }
            }
         }
         for each(_loc4_ in _loc3_..variable)
         {
            _loc5_ = <EMAIL>();
            _loc6_[_loc5_] = getDefinitionByName(_loc4_.@type);
         }
         return _loc6_;
      }
      
      public static function eval(param1:String, param2:Object = null) : *
      {
         var paths:Array;
         var li:int = 0;
         var path:String = null;
         var num:Number = NaN;
         var value:String = param1;
         var root:Object = param2;
         var si:int = int(value.indexOf("::"));
         if(si != -1)
         {
            li = int(value.indexOf(".",si));
            if(li == -1)
            {
               li = value.length;
            }
            root = getDefinitionByName(value.substr(0,li));
            value = value.substr(li);
         }
         paths = value.split(/\[|\]|\./);
         try
         {
            if(value.charAt(0) > "A" && value.charAt(0) < "Z")
            {
               root = getDefinitionByName(paths.shift());
            }
            for each(path in paths)
            {
               if(!root)
               {
                  return null;
               }
               if(path != "")
               {
                  num = Number(path);
                  if(isNaN(num))
                  {
                     root = root[path];
                  }
                  else
                  {
                     root = root is DisplayObjectContainer ? root.getChildAt(int(num)) : root[num];
                  }
               }
            }
         }
         catch(e:Error)
         {
            Debug.trace("REF","反射失败！ReflectManager.eval() " + e.message);
            return null;
         }
         return root;
      }
      
      public static function getDefinitionByName(param1:String) : Class
      {
         var name:String = param1;
         if(name == "*")
         {
            name = "Object";
         }
         try
         {
            return getDefinitionByName(name) as Class;
         }
         catch(e:ReferenceError)
         {
            Debug.trace("REF","反射类" + name + "失败！");
         }
         return null;
      }
      
      public static function getMetaData(param1:*, param2:String = null, param3:String = null) : XML
      {
         var prop:XML = null;
         var obj:* = param1;
         var property:String = param2;
         var metaName:String = param3;
         var xml:XML = getDescribeType(obj);
         if(property == null)
         {
            prop = xml.factory[0];
         }
         else
         {
            prop = xml..*.(Boolean(hasOwnProperty("@name")) && @name == property)[0];
         }
         if(prop)
         {
            if(metaName)
            {
               return prop.metadata.(@name == metaName)[0];
            }
            return prop.metadata[0];
         }
         return null;
      }
      
      public static function getClass(param1:*) : Class
      {
         if(param1 == null)
         {
            return null;
         }
         if(param1 is Class)
         {
            return param1;
         }
         return param1.constructor as Class;
      }
      
      public static function getQName(param1:*) : QName
      {
         var _loc2_:Array = getQualifiedClassName(param1).split("::");
         if(_loc2_.length == 2)
         {
            return new QName(_loc2_[0],_loc2_[1]);
         }
         return new QName(null,_loc2_[0]);
      }
   }
}

