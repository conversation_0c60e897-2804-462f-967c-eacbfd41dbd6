package cutShoter.view
{
   import cutShoter.events.EventManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.Event;
   import flash.events.MouseEvent;
   
   [Embed(source="/_assets/assets.swf", symbol="symbol140")]
   public class mc_completed extends MovieClip
   {
      
      public var mc_connect:MovieClip;
      
      public var btn_close:SimpleButton;
      
      public var btn_OK:SimpleButton;
      
      public var mc_title:MovieClip;
      
      public var mc_content:MovieClip;
      
      public function mc_completed()
      {
         super();
         if(!stage)
         {
            this.addEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         else
         {
            this.init();
         }
      }
      
      private function init(param1:Event = null) : void
      {
         if(param1)
         {
            this.removeEventListener(Event.ADDED_TO_STAGE,this.init);
         }
         this.btn_close.addEventListener(MouseEvent.CLICK,this.closeHandler);
         this.btn_OK.addEventListener(MouseEvent.CLICK,this.closeHandler);
      }
      
      private function closeHandler(param1:MouseEvent) : void
      {
         dispatchEvent(new EventManager(EventManager.SUBMITCOMPLETED));
         if(parent)
         {
            this.parent.removeChild(this);
         }
      }
   }
}

