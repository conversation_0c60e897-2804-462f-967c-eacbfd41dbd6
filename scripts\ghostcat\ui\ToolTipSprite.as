package ghostcat.ui
{
   import flash.display.DisplayObject;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import ghostcat.display.GBase;
   import ghostcat.display.IToolTipManagerClient;
   import ghostcat.events.TickEvent;
   import ghostcat.ui.tooltip.IToolTipSkin;
   import ghostcat.ui.tooltip.ToolTipSkin;
   import ghostcat.util.Util;
   import ghostcat.util.core.ClassFactory;
   
   public class ToolTipSprite extends GBase
   {
      
      private static var _instance:ToolTipSprite;
      
      public static var defaultSkin:ClassFactory = new ClassFactory(ToolTipSkin);
      
      public var delay:int = 250;
      
      public var cooldown:int = 250;
      
      public var onlyWithClasses:Array;
      
      public var target:DisplayObject;
      
      private var toolTipObjs:Object;
      
      private var delayTimer:Timer;
      
      private var delayCooldown:Timer;
      
      public function ToolTipSprite()
      {
         super();
         this.acceptContentPosition = false;
         this.mouseEnabled = this.mouseChildren = false;
         this.enabledTick = true;
         if(!_instance)
         {
            _instance = this;
         }
      }
      
      public static function get instance() : ToolTipSprite
      {
         return _instance;
      }
      
      public function get obj() : GBase
      {
         return content as GBase;
      }
      
      public function registerToolTipObj(param1:String, param2:GBase) : void
      {
         this.toolTipObjs[param1] = param2;
      }
      
      override public function get data() : *
      {
         return this.obj.data;
      }
      
      override public function set data(param1:*) : void
      {
         this.obj.data = param1;
      }
      
      override protected function init() : void
      {
         super.init();
         stage.addEventListener(MouseEvent.MOUSE_OVER,this.mouseOverHandler);
         stage.addEventListener(MouseEvent.MOUSE_OUT,this.mouseOutHandler);
      }
      
      override protected function tickHandler(param1:TickEvent) : void
      {
         if(Boolean(content) && Boolean(this.target))
         {
            (content as IToolTipSkin).positionTo(this.target);
         }
      }
      
      private function mouseOutHandler(param1:MouseEvent) : void
      {
      }
      
      private function mouseOverHandler(param1:MouseEvent) : void
      {
         var _loc2_:DisplayObject = this.findToolTipTarget(param1.target as DisplayObject);
         if(_loc2_ != this.target)
         {
            this.target = _loc2_;
            if(this.target)
            {
               this.delayShow(this.delay);
            }
            else if(this.content)
            {
               this.hide();
            }
         }
      }
      
      private function findToolTipTarget(param1:DisplayObject) : DisplayObject
      {
         var _loc2_:DisplayObject = param1;
         while(Boolean(_loc2_) && _loc2_.parent != _loc2_)
         {
            if(_loc2_ is IToolTipManagerClient && (_loc2_ as IToolTipManagerClient).toolTip && (this.onlyWithClasses == null || Util.isIn(cursor,this.onlyWithClasses)))
            {
               return _loc2_;
            }
            _loc2_ = _loc2_.parent;
         }
         return null;
      }
      
      public function delayShow(param1:int) : void
      {
         if(this.delayCooldown)
         {
            this.delayCooldown.delay = 1;
            param1 = 1;
         }
         if(this.delayTimer)
         {
            this.delayTimer.delay = param1;
         }
         else
         {
            this.delayTimer = new Timer(param1,1);
            this.delayTimer.addEventListener(TimerEvent.TIMER_COMPLETE,this.show);
            this.delayTimer.start();
         }
      }
      
      private function show(param1:TimerEvent) : void
      {
         var _loc2_:IToolTipManagerClient = null;
         this.delayTimer.removeEventListener(TimerEvent.TIMER_COMPLETE,this.show);
         this.delayTimer = null;
         if(this.target is IToolTipManagerClient)
         {
            _loc2_ = this.target as IToolTipManagerClient;
            this.showToolTip(this.target,_loc2_.toolTip,_loc2_.toolTipObj);
         }
      }
      
      public function showToolTip(param1:DisplayObject, param2:*, param3:* = null) : void
      {
         this.target = param1;
         var _loc4_:* = param3;
         if(_loc4_ is String)
         {
            _loc4_ = this.toolTipObjs[_loc4_];
         }
         if(_loc4_ is Class)
         {
            _loc4_ = new _loc4_();
         }
         if(!_loc4_)
         {
            _loc4_ = defaultSkin;
         }
         setContent(_loc4_);
         (content as IToolTipSkin).data = param2;
         (content as IToolTipSkin).show(param1);
      }
      
      public function hide() : void
      {
         setContent(null);
         if(this.delayCooldown)
         {
            this.delayCooldown.delay = this.cooldown;
         }
         else
         {
            this.delayCooldown = new Timer(this.cooldown,1);
            this.delayCooldown.addEventListener(TimerEvent.TIMER_COMPLETE,this.removeCooldown);
            this.delayCooldown.start();
         }
      }
      
      private function removeCooldown(param1:TimerEvent) : void
      {
         this.delayCooldown.removeEventListener(TimerEvent.TIMER_COMPLETE,this.removeCooldown);
         this.delayCooldown = null;
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         stage.removeEventListener(MouseEvent.MOUSE_OVER,this.mouseOverHandler);
         stage.removeEventListener(MouseEvent.MOUSE_OUT,this.mouseOutHandler);
         super.destory();
      }
   }
}

