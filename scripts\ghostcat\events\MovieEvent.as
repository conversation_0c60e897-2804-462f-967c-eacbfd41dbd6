package ghostcat.events
{
   import flash.events.Event;
   
   public class MovieEvent extends Event
   {
      
      public static const MOVIE_START:String = "label_start";
      
      public static const MOVIE_END:String = "label_end";
      
      public static const MOVIE_EMPTY:String = "label_empty";
      
      public static const MOVIE_ERROR:String = "label_error";
      
      public var labelName:String;
      
      public function MovieEvent(param1:String, param2:Boolean = false, param3:Boolean = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         var _loc1_:MovieEvent = new MovieEvent(type,bubbles,cancelable);
         _loc1_.labelName = this.labelName;
         return _loc1_;
      }
   }
}

