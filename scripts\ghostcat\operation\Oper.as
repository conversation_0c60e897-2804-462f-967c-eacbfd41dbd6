package ghostcat.operation
{
   import flash.events.EventDispatcher;
   import ghostcat.events.OperationEvent;
   import ghostcat.util.core.AbstractUtil;
   
   public class Oper extends EventDispatcher implements IOper
   {
      
      public static const NONE:int = 0;
      
      public static const WAIT:int = 1;
      
      public static const RUN:int = 2;
      
      public static const END:int = 3;
      
      public var id:String;
      
      public var queue:Queue;
      
      public var step:int = 0;
      
      public var lastResult:*;
      
      public var immediately:Boolean = false;
      
      public var continueWhenFail:Boolean = true;
      
      public function Oper()
      {
         super();
         AbstractUtil.preventConstructor(this,Oper);
      }
      
      public function execute() : void
      {
         var _loc1_:OperationEvent = new OperationEvent(OperationEvent.OPERATION_START);
         _loc1_.oper = this;
         dispatchEvent(_loc1_);
         if(this.queue)
         {
            _loc1_ = new OperationEvent(OperationEvent.CHILD_OPERATION_START);
            _loc1_.oper = this.queue;
            _loc1_.childOper = this;
            this.queue.dispatchEvent(_loc1_);
         }
         this.step = RUN;
         if(this.immediately)
         {
            _loc1_ = new OperationEvent(OperationEvent.OPERATION_COMPLETE);
            _loc1_.oper = this;
            dispatchEvent(_loc1_);
            if(this.queue)
            {
               _loc1_ = new OperationEvent(OperationEvent.CHILD_OPERATION_COMPLETE);
               _loc1_.oper = this.queue;
               _loc1_.childOper = this;
               dispatchEvent(_loc1_);
            }
         }
      }
      
      public function result(param1:* = null) : void
      {
         this.lastResult = param1;
         this.end(param1);
         var _loc2_:OperationEvent = new OperationEvent(OperationEvent.OPERATION_COMPLETE);
         _loc2_.oper = this;
         _loc2_.result = param1;
         dispatchEvent(_loc2_);
         if(this.queue)
         {
            _loc2_ = new OperationEvent(OperationEvent.CHILD_OPERATION_COMPLETE);
            _loc2_.oper = this.queue;
            _loc2_.childOper = this;
            _loc2_.result = param1;
            dispatchEvent(_loc2_);
            this.queue = null;
         }
         this.step = END;
      }
      
      public function fault(param1:* = null) : void
      {
         this.lastResult = param1;
         this.end(param1);
         var _loc2_:OperationEvent = new OperationEvent(OperationEvent.OPERATION_ERROR);
         _loc2_.oper = this;
         _loc2_.result = param1;
         dispatchEvent(_loc2_);
         if(this.queue)
         {
            _loc2_ = new OperationEvent(OperationEvent.CHILD_OPERATION_ERROR);
            _loc2_.oper = this.queue;
            _loc2_.childOper = this;
            _loc2_.result = param1;
            dispatchEvent(_loc2_);
            this.queue = null;
         }
         this.step = END;
      }
      
      public function commit(param1:Queue = null) : void
      {
         if(!param1)
         {
            param1 = Queue.defaultQueue;
         }
         param1.commitChild(this);
      }
      
      protected function end(param1:* = null) : void
      {
      }
      
      public function halt() : void
      {
         this.end();
         if(this.queue)
         {
            this.queue.haltChild(this);
         }
      }
   }
}

