package ghostcat.display.graphics
{
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import ghostcat.display.GBase;
   import ghostcat.skin.PointSkin;
   import ghostcat.ui.CursorSprite;
   import ghostcat.util.core.ClassFactory;
   import ghostcat.util.display.Geom;
   
   public class DragPoint extends GBase
   {
      
      public static var defaultSkin:ClassFactory = new ClassFactory(PointSkin);
      
      public var lockX:Boolean = false;
      
      public var lockY:Boolean = false;
      
      public var bounds:Rectangle;
      
      private var localMousePoint:Point;
      
      public function DragPoint(param1:* = null, param2:Point = null, param3:Boolean = true)
      {
         if(!param1)
         {
            param1 = defaultSkin.newInstance();
         }
         super(param1,param3);
         if(!param2)
         {
            param2 = new Point();
         }
         this.position = param2;
         addEventListener(MouseEvent.MOUSE_DOWN,this.onMouseDownHandler);
         this.cursor = "drag";
         this.enabled = enabled;
         this.positionCall.frame = false;
      }
      
      public function get mouseDown() : Boolean
      {
         return this.localMousePoint != null;
      }
      
      private function onMouseDownHandler(param1:MouseEvent) : void
      {
         if(enabled)
         {
            this.localMousePoint = new Point(x - parent.mouseX,y - parent.mouseY);
            stage.addEventListener(MouseEvent.MOUSE_UP,this.onMouseUpHandler);
            stage.addEventListener(MouseEvent.MOUSE_MOVE,this.onMouseMoveHandler);
            if(CursorSprite.instance)
            {
               CursorSprite.instance.lock = true;
            }
         }
      }
      
      private function onMouseUpHandler(param1:MouseEvent) : void
      {
         this.localMousePoint = null;
         invalidatePosition();
         stage.removeEventListener(MouseEvent.MOUSE_UP,this.onMouseUpHandler);
         stage.removeEventListener(MouseEvent.MOUSE_MOVE,this.onMouseMoveHandler);
         if(CursorSprite.instance)
         {
            CursorSprite.instance.lock = false;
         }
      }
      
      private function onMouseMoveHandler(param1:MouseEvent) : void
      {
         if(!this.lockX)
         {
            x = parent.mouseX + this.localMousePoint.x;
         }
         if(!this.lockY)
         {
            y = parent.mouseY + this.localMousePoint.y;
         }
         if(this.bounds)
         {
            Geom.forcePointInside(this,this.bounds);
         }
      }
      
      override public function set enabled(param1:Boolean) : void
      {
         mouseEnabled = super.enabled = param1;
      }
   }
}

