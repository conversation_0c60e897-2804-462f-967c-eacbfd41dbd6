package swfParser
{
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   
   public class SWFParser
   {
      
      protected var _data:Data;
      
      protected var _version:uint;
      
      protected var _size:uint;
      
      protected var _rect:Rectangle;
      
      protected var _frameRate:uint;
      
      protected var _frames:uint;
      
      public function SWFParser(param1:ByteArray)
      {
         var _loc2_:String = null;
         var _loc3_:ByteArray = null;
         super();
         if(param1)
         {
            param1.position = 0;
            _loc2_ = param1.readUTFBytes(3);
            this._version = param1.readUnsignedByte();
            this._size = param1.readUnsignedInt();
            this._data = new Data(param1);
            if(_loc2_ == "CWS")
            {
               _loc3_ = new ByteArray();
               this._data.readBytes(_loc3_);
               _loc3_.position = 0;
               _loc3_.uncompress();
               _loc3_.position = 0;
               this._data = new Data(_loc3_);
            }
            this._rect = this._data.readRect();
            this._frameRate = this._data.readUnsignedShort() >> 8;
            this._frames = this._data.readShort();
         }
      }
      
      public function parseTags(param1:*, param2:Boolean, param3:uint = 0, param4:Data = null) : Array
      {
         var _loc6_:uint = 0;
         var _loc7_:uint = 0;
         var _loc10_:Tag = null;
         var _loc11_:ByteArray = null;
         if(param4 == null)
         {
            param4 = this._data;
         }
         var _loc5_:* = -1;
         var _loc8_:uint = uint(param4.position);
         var _loc9_:Array = [];
         while(Boolean(param4.bytesAvailable) && _loc5_ != param3)
         {
            _loc5_ = int(param4.readUnsignedShort());
            _loc6_ = uint(_loc5_ & 0x3F);
            if(_loc6_ == 63)
            {
               _loc6_ = param4.readUnsignedInt();
            }
            _loc5_ >>= 6;
            _loc7_ = uint(param4.position);
            if(_loc5_ == param1 || (param1 is Array ? (param1 as Array).indexOf(_loc5_) != -1 : false))
            {
               _loc10_ = new Tag(_loc5_,param4.position,_loc6_);
               if(param2)
               {
                  _loc11_ = new ByteArray();
                  param4.readBytes(_loc11_,0,_loc10_.length);
                  _loc11_.position = 0;
                  _loc10_.data = new Data(_loc11_);
               }
               _loc9_.push(_loc10_);
            }
            if(param4.position == _loc7_)
            {
               param4.position += _loc6_;
            }
         }
         param4.position = _loc8_;
         return _loc9_;
      }
      
      public function get data() : Data
      {
         return this._data;
      }
      
      public function get ver() : uint
      {
         return this._version;
      }
      
      public function get size() : uint
      {
         return this._size;
      }
      
      public function get rect() : Rectangle
      {
         return this._rect;
      }
      
      public function get frameRate() : uint
      {
         return this._frameRate;
      }
      
      public function get frames() : uint
      {
         return this._frames;
      }
   }
}

