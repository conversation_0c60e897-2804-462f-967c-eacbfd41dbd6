package ghostcat.ui.controls
{
   import flash.geom.ColorTransform;
   import ghostcat.operation.Oper;
   import ghostcat.operation.SoundOper;
   import ghostcat.operation.effect.IEffect;
   
   public class GButtonState
   {
      
      public var colorTransform:ColorTransform;
      
      public var filters:Array;
      
      public var skin:*;
      
      public var oper:Oper;
      
      public var sound:*;
      
      public function GButtonState()
      {
         super();
      }
      
      public function parse(param1:GButtonBase) : void
      {
         if(this.skin)
         {
            param1.setPartConetent(this.skin);
         }
         param1.content.transform.colorTransform = this.colorTransform ? this.colorTransform : new ColorTransform();
         param1.content.filters = this.filters ? this.filters : [];
         if(this.oper)
         {
            if(this.oper is IEffect)
            {
               (this.oper as IEffect).target = param1.content;
            }
            this.oper.execute();
         }
         if(this.sound)
         {
            if(!(this.sound is SoundOper))
            {
               this.sound = new SoundOper(this.sound);
            }
            (this.sound as SoundOper).execute();
         }
      }
      
      public function clone() : GButtonState
      {
         var _loc1_:GButtonState = new GButtonState();
         _loc1_.colorTransform = this.colorTransform;
         _loc1_.filters = this.filters;
         _loc1_.skin = this.skin;
         _loc1_.oper = this.oper;
         return _loc1_;
      }
   }
}

