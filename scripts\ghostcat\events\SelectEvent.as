package ghostcat.events
{
   import flash.events.Event;
   import flash.geom.Rectangle;
   
   public class SelectEvent extends Event
   {
      
      public static const SELECT:String = "select";
      
      public var rect:Rectangle;
      
      public function SelectEvent(param1:String, param2:<PERSON><PERSON>an = false, param3:<PERSON><PERSON>an = false)
      {
         super(param1,param2,param3);
      }
      
      override public function clone() : Event
      {
         var _loc1_:SelectEvent = new SelectEvent(type,bubbles,cancelable);
         _loc1_.rect = this.rect;
         return _loc1_;
      }
   }
}

