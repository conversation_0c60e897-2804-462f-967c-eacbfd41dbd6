package ghostcat.parse.display
{
   import flash.display.Graphics;
   import ghostcat.parse.graphics.GraphicsRect;
   import ghostcat.parse.graphics.IGraphicsFill;
   import ghostcat.parse.graphics.IGraphicsLineStyle;
   
   public class RectParse extends ShapeParse
   {
      
      public var rect:GraphicsRect;
      
      public function RectParse(param1:GraphicsRect, param2:IGraphicsLineStyle = null, param3:IGraphicsFill = null, param4:Grid9Parse = null, param5:Boolean = false)
      {
         super(null,param2,param3,param4,param5);
         this.rect = param1;
      }
      
      override protected function parseBaseShape(param1:Graphics) : void
      {
         super.parseBaseShape(param1);
         if(this.rect)
         {
            this.rect.parseGraphics(param1);
         }
      }
   }
}

