package ghostcat.display
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.events.TimerEvent;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.Timer;
   import ghostcat.events.GEvent;
   import ghostcat.events.MoveEvent;
   import ghostcat.events.ResizeEvent;
   import ghostcat.events.TickEvent;
   import ghostcat.util.Tick;
   import ghostcat.util.core.UniqueCall;
   
   public class GBase extends GSprite implements IGBase
   {
      
      private var _enabled:Boolean = true;
      
      private var _selected:Boolean = false;
      
      private var _paused:Boolean = false;
      
      private var _enabledTick:Boolean = false;
      
      private var _cursor:*;
      
      private var _toolTip:*;
      
      private var _toolTipObj:*;
      
      protected var _data:Object;
      
      private var _oldPosition:Point = new Point();
      
      private var _position:Point = new Point();
      
      public var enabledDelayUpdate:Boolean = true;
      
      public var delayUpatePosition:Boolean = false;
      
      public var owner:DisplayObject;
      
      public var self:GBase = this;
      
      public var priority:int = 0;
      
      protected var positionCall:UniqueCall;
      
      protected var sizeCall:UniqueCall;
      
      protected var displayListCall:UniqueCall;
      
      private var _bitmap:Bitmap;
      
      private var _asBitmap:Boolean = false;
      
      private var _refreshInterval:int = 0;
      
      private var _refreshTimer:Timer;
      
      public function GBase(param1:* = null, param2:Boolean = true)
      {
         this.positionCall = new UniqueCall(this.vaildPosition,true);
         this.sizeCall = new UniqueCall(this.vaildSize,true);
         this.displayListCall = new UniqueCall(this.vaildDisplayList);
         super(param1,param2);
      }
      
      public function get data() : *
      {
         return this._data;
      }
      
      public function set data(param1:*) : void
      {
         this._data = param1;
         dispatchEvent(new GEvent(GEvent.DATA_CHANGE));
      }
      
      public function get toolTip() : *
      {
         return this._toolTip;
      }
      
      public function set toolTip(param1:*) : void
      {
         this._toolTip = param1;
      }
      
      public function get toolTipObj() : *
      {
         return this._toolTipObj;
      }
      
      public function set toolTipObj(param1:*) : void
      {
         this._toolTipObj = param1;
      }
      
      public function get cursor() : *
      {
         return this._cursor;
      }
      
      public function set cursor(param1:*) : void
      {
         this._cursor = param1;
      }
      
      public function get selected() : Boolean
      {
         return this._selected;
      }
      
      public function set selected(param1:Boolean) : void
      {
         this._selected = param1;
      }
      
      public function get paused() : Boolean
      {
         return this._paused;
      }
      
      public function set paused(param1:Boolean) : void
      {
         if(this._paused == param1)
         {
            return;
         }
         this._paused = param1;
         if(!this._paused && this._enabledTick)
         {
            Tick.instance.addEventListener(TickEvent.TICK,this.tickHandler,false,this.priority);
         }
         else
         {
            Tick.instance.removeEventListener(TickEvent.TICK,this.tickHandler);
         }
      }
      
      public function get enabledTick() : Boolean
      {
         return this._enabledTick;
      }
      
      public function set enabledTick(param1:Boolean) : void
      {
         if(this._enabledTick == param1)
         {
            return;
         }
         this._enabledTick = param1;
         if(!this._paused && this._enabledTick)
         {
            Tick.instance.addEventListener(TickEvent.TICK,this.tickHandler);
         }
         else
         {
            Tick.instance.removeEventListener(TickEvent.TICK,this.tickHandler);
         }
      }
      
      public function get enabled() : Boolean
      {
         return this._enabled;
      }
      
      public function set enabled(param1:Boolean) : void
      {
         this._enabled = param1;
      }
      
      override public function set visible(param1:Boolean) : void
      {
         var _loc2_:GEvent = null;
         if(param1 == visible)
         {
            return;
         }
         if(param1)
         {
            _loc2_ = new GEvent(GEvent.SHOW,false,true);
         }
         else
         {
            _loc2_ = new GEvent(GEvent.HIDE,false,true);
         }
         dispatchEvent(_loc2_);
         if(_loc2_.isDefaultPrevented())
         {
            return;
         }
         super.visible = param1;
      }
      
      override public function set x(param1:Number) : void
      {
         if(this.x == param1)
         {
            return;
         }
         this._oldPosition.x = super.x;
         this.position.x = param1;
         if(!this.delayUpatePosition)
         {
            super.x = param1;
         }
         if(this.enabledDelayUpdate)
         {
            this.positionCall.invalidate();
         }
      }
      
      override public function get x() : Number
      {
         return this.position.x;
      }
      
      override public function set y(param1:Number) : void
      {
         if(this.y == param1)
         {
            return;
         }
         this._oldPosition.y = super.y;
         this.position.y = param1;
         if(!this.delayUpatePosition)
         {
            super.y = param1;
         }
         if(this.enabledDelayUpdate)
         {
            this.positionCall.invalidate();
         }
      }
      
      override public function get y() : Number
      {
         return this.position.y;
      }
      
      public function setPosition(param1:Point, param2:Boolean = false) : void
      {
         var _loc3_:Point = new Point(super.x,super.y);
         if(!_loc3_.equals(param1))
         {
            this._oldPosition = _loc3_;
            this._position = param1;
            if(!this.delayUpatePosition)
            {
               super.x = param1.x;
               super.y = param1.y;
            }
         }
         if(this.enabledDelayUpdate)
         {
            this.vaildPosition(param2);
         }
      }
      
      public function set position(param1:Point) : void
      {
         this.setPosition(param1);
      }
      
      public function get position() : Point
      {
         return this._position;
      }
      
      public function get oldPosition() : Point
      {
         return this._oldPosition;
      }
      
      public function get positionOffest() : Point
      {
         return this.position.subtract(this._oldPosition);
      }
      
      override public function set width(param1:Number) : void
      {
         if(super.width == param1)
         {
            return;
         }
         super.width = param1;
         if(this.enabledDelayUpdate)
         {
            this.sizeCall.invalidate();
         }
      }
      
      override public function set height(param1:Number) : void
      {
         if(super.height == param1)
         {
            return;
         }
         super.height = param1;
         if(this.enabledDelayUpdate)
         {
            this.sizeCall.invalidate();
         }
      }
      
      public function setSize(param1:Number, param2:Number, param3:Boolean = false) : void
      {
         if(super.width == param1 && super.height == param2)
         {
            return;
         }
         super.width = param1;
         super.height = param2;
         this.vaildSize(param3);
      }
      
      public function get size() : Point
      {
         return new Point(width,height);
      }
      
      override protected function init() : void
      {
         super.init();
         this.createChildren();
      }
      
      protected function createChildren() : void
      {
      }
      
      public function vaildNow() : void
      {
         this.vaildPosition();
         this.vaildSize();
         this.vaildDisplayList();
      }
      
      public function invalidatePosition() : void
      {
         this.positionCall.invalidate();
      }
      
      public function invalidateSize() : void
      {
         this.sizeCall.invalidate();
      }
      
      public function invalidateDisplayList() : void
      {
         this.displayListCall.invalidate();
      }
      
      public function vaildPosition(param1:Boolean = false) : void
      {
         var _loc2_:MoveEvent = null;
         if(super.x != this.position.x)
         {
            super.x = this.position.x;
         }
         if(super.y != this.position.y)
         {
            super.y = this.position.y;
         }
         this.updatePosition();
         if(!param1)
         {
            _loc2_ = new MoveEvent(MoveEvent.MOVE);
            _loc2_.oldPosition = this._oldPosition;
            _loc2_.newPosition = this.position;
            dispatchEvent(_loc2_);
         }
         this._oldPosition = this.position.clone();
      }
      
      public function vaildSize(param1:Boolean = false) : void
      {
         var _loc2_:ResizeEvent = null;
         this.updateSize();
         if(!param1)
         {
            _loc2_ = new ResizeEvent(ResizeEvent.RESIZE);
            _loc2_.size = new Point(width,height);
            dispatchEvent(_loc2_);
            if(parent)
            {
               _loc2_ = new ResizeEvent(ResizeEvent.CHILD_RESIZE);
               _loc2_.size = new Point(width,height);
               _loc2_.child = this;
               parent.dispatchEvent(_loc2_);
            }
         }
      }
      
      public function vaildDisplayList(param1:Boolean = false) : void
      {
         this.updateDisplayList();
         if(!param1)
         {
            dispatchEvent(new GEvent(GEvent.UPDATE_COMPLETE));
         }
      }
      
      protected function updatePosition() : void
      {
      }
      
      protected function updateSize() : void
      {
      }
      
      protected function updateDisplayList() : void
      {
      }
      
      public function tick(param1:int) : void
      {
         var _loc2_:TickEvent = new TickEvent(TickEvent.TICK);
         _loc2_.interval = param1;
         this.tickHandler(_loc2_);
      }
      
      protected function tickHandler(param1:TickEvent) : void
      {
         this.vaildDisplayList();
      }
      
      public function set asBitmap(param1:Boolean) : void
      {
         if(!content)
         {
            return;
         }
         if(param1)
         {
            content.visible = false;
            this.reRenderBitmap();
         }
         else
         {
            content.visible = true;
            if(this._bitmap)
            {
               this._bitmap.bitmapData.dispose();
               this._bitmap.parent.removeChild(this._bitmap);
               this._bitmap = null;
            }
         }
      }
      
      public function get asBitmap() : Boolean
      {
         return this._asBitmap;
      }
      
      public function reRenderBitmap() : void
      {
         var _loc1_:Rectangle = this._bitmap ? this._bitmap.getBounds(this) : null;
         var _loc2_:Rectangle = content.getBounds(this);
         if(!_loc1_ || !_loc2_.equals(_loc1_))
         {
            if(this._bitmap)
            {
               removeChild(this._bitmap);
               this._bitmap.bitmapData.dispose();
            }
            this._bitmap = new Bitmap(new BitmapData(Math.ceil(_loc2_.width),Math.ceil(_loc2_.height),true,0));
            this._bitmap.x = _loc2_.x;
            this._bitmap.y = _loc2_.y;
            $addChild(this._bitmap);
         }
         var _loc3_:Matrix = new Matrix();
         _loc3_.translate(-_loc2_.x,-_loc2_.y);
         this._bitmap.bitmapData.draw(content,_loc3_);
      }
      
      public function get refreshInterval() : int
      {
         return this._refreshInterval;
      }
      
      public function set refreshInterval(param1:int) : void
      {
         if(this._refreshInterval == param1)
         {
            return;
         }
         this._refreshInterval = param1;
         if(param1 == 0)
         {
            if(this._refreshTimer)
            {
               this._refreshTimer.removeEventListener(TimerEvent.TIMER,this.refreshHandler);
               this._refreshTimer.stop();
               this._refreshTimer = null;
            }
         }
         else if(!this._refreshTimer)
         {
            this._refreshTimer = new Timer(param1,int.MAX_VALUE);
            this._refreshTimer.addEventListener(TimerEvent.TIMER,this.refreshHandler);
            this._refreshTimer.start();
         }
         else
         {
            this._refreshTimer.delay = param1;
         }
      }
      
      protected function refreshHandler(param1:TimerEvent) : void
      {
         this.vaildDisplayList();
      }
      
      override public function destory() : void
      {
         if(destoryed)
         {
            return;
         }
         var _loc1_:GEvent = new GEvent(GEvent.REMOVE,false,true);
         dispatchEvent(_loc1_);
         if(_loc1_.isDefaultPrevented())
         {
            return;
         }
         this.enabledTick = false;
         this.refreshInterval = 0;
         this.asBitmap = false;
         super.destory();
      }
   }
}

