package ghostcat.text
{
   public class UBB
   {
      
      private static const COLORS:Array = ["red","blue","green","yellow","fuchsia","aqua","black","white","gray"];
      
      private static const COLOR_REPS:Array = ["#FF0000","#0000FF","#00FF00","#FFFF00","#FF00FF","#00FFFF","#000000","#FFFFFF","#808080"];
      
      public function UBB()
      {
         super();
      }
      
      public static function decode(param1:String) : String
      {
         param1 = param1.replace(/\[(b|i|u)\]/ig,"<$1>");
         param1 = param1.replace(/\[\/(b|i|u)\]/ig,"</$1>");
         param1 = param1.replace(/\[(color|size|face|align)=(.*?)]/ig,replFN);
         param1 = param1.replace(/\[\/(color|size|face|align)\]/ig,"</font>");
         param1 = param1.replace(/\[img\](.*?)\[\/img\]/ig,"<img src=\'$1\'/>");
         param1 = param1.replace(/\[url\](.*?)\[\/url\]/ig,"<a href=\'$1\'/>$1</a>");
         param1 = param1.replace(/\[url=(.*?)\](.*)?\[\/url\]/ig,"<a href=\'$1\'/>$2</a>");
         param1 = param1.replace(/\[email\](.*?)\[\/email\]/ig,"<a href=\'mailto:$1\'>$1</a>");
         return param1.replace(/\[email=(.*?)\](.*)?\[\/email\]/ig,"<a href=\'mailto:$1\'>$2</a>");
      }
      
      private static function replFN() : String
      {
         var _loc5_:int = 0;
         var _loc2_:String = arguments[2];
         var _loc3_:String = _loc2_.charAt(0);
         var _loc4_:String = _loc2_.charAt(_loc2_.length - 1);
         if(_loc4_ == _loc3_ && (_loc4_ == "\'" || _loc4_ == "\""))
         {
            _loc2_ = _loc2_.slice(1,_loc2_.length - 1);
         }
         if(arguments[1].toLowerCase() == "color")
         {
            _loc5_ = int(COLORS.indexOf(_loc2_.toLowerCase()));
            if(_loc5_ != -1)
            {
               _loc2_ = COLOR_REPS[_loc5_];
            }
         }
         return "<font " + arguments[1] + "=\"" + _loc2_ + "\">";
      }
   }
}

