package ghostcat.parse.display
{
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.Graphics;
   import ghostcat.parse.DisplayParse;
   import ghostcat.parse.graphics.GraphicsEndFill;
   import ghostcat.parse.graphics.IGraphicsFill;
   import ghostcat.parse.graphics.IGraphicsLineStyle;
   
   public class ShapeParse extends DisplayParse
   {
      
      public var reset:Boolean;
      
      public var line:IGraphicsLineStyle;
      
      public var fill:IGraphicsFill;
      
      public var parses:Array;
      
      public var grid9:Grid9Parse;
      
      public function ShapeParse(param1:Array, param2:IGraphicsLineStyle = null, param3:IGraphicsFill = null, param4:Grid9Parse = null, param5:Boolean = false)
      {
         super();
         this.parses = param1;
         this.reset = param5;
         this.line = param2;
         this.fill = param3;
         this.grid9 = param4;
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         super.parseGraphics(param1);
         if(this.reset)
         {
            param1.clear();
         }
         if(this.line)
         {
            this.line.parse(param1);
         }
         if(this.fill)
         {
            this.fill.parse(param1);
         }
         this.parseBaseShape(param1);
         if(this.fill)
         {
            new GraphicsEndFill().parse(param1);
         }
      }
      
      protected function parseBaseShape(param1:Graphics) : void
      {
         var _loc2_:int = 0;
         if(this.parses)
         {
            _loc2_ = 0;
            while(_loc2_ < this.parses.length)
            {
               (this.parses[_loc2_] as DisplayParse).parse(param1);
               _loc2_++;
            }
         }
      }
      
      override public function parseBitmapData(param1:BitmapData) : void
      {
         super.parseBitmapData(param1);
         param1.draw(createShape());
      }
      
      override public function parseDisplay(param1:DisplayObject) : void
      {
         super.parseDisplay(param1);
         if(this.grid9)
         {
            this.grid9.parse(param1);
         }
      }
   }
}

