package ghostcat.ui
{
   import flash.display.DisplayObject;
   import flash.events.MouseEvent;
   import ghostcat.display.GBase;
   import ghostcat.display.IGBase;
   import ghostcat.ui.controls.GButtonBase;
   import ghostcat.util.ReflectUtil;
   import ghostcat.util.core.ClassFactory;
   import ghostcat.util.display.SearchUtil;
   
   public final class UIBuilder
   {
      
      public function UIBuilder()
      {
         super();
      }
      
      public static function buildAll(param1:GBase, param2:Object = null, param3:Boolean = false) : void
      {
         var _loc8_:DisplayObject = null;
         var _loc9_:String = null;
         var _loc10_:ClassFactory = null;
         var _loc11_:GBase = null;
         var _loc4_:DisplayObject = param1.content;
         var _loc5_:Array = SearchUtil.findChildrenByClass(_loc4_,DisplayObject);
         var _loc6_:Object = ReflectUtil.getPropertyTypeList(param1,true);
         var _loc7_:int = 0;
         while(_loc7_ < _loc5_.length)
         {
            _loc8_ = _loc5_[_loc7_] as DisplayObject;
            _loc9_ = _loc8_.name;
            if(_loc6_[_loc9_])
            {
               if(param2)
               {
                  if(param2[_loc9_])
                  {
                     if(param2[_loc9_] is Class)
                     {
                        _loc10_ = new ClassFactory(param2[_loc9_] as Class);
                     }
                     else if(param2[_loc9_] is ClassFactory)
                     {
                        _loc10_ = param2[_loc9_] as ClassFactory;
                     }
                  }
                  else if(!param3)
                  {
                     _loc10_ = new ClassFactory(_loc6_[_loc9_] as Class);
                  }
               }
               else
               {
                  _loc10_ = new ClassFactory(_loc6_[_loc9_] as Class);
               }
               if(_loc10_)
               {
                  if(_loc10_.params)
                  {
                     _loc10_.params[0] = _loc8_;
                  }
                  else
                  {
                     _loc10_.params = [_loc8_];
                  }
                  _loc11_ = _loc10_.newInstance();
                  _loc11_.owner = param1;
                  param1[_loc9_] = _loc11_;
                  delete _loc6_[_loc9_];
               }
            }
            _loc7_++;
         }
      }
      
      public static function build(param1:GBase, param2:String, param3:* = null) : DisplayObject
      {
         var _loc4_:DisplayObject = SearchUtil.findChildByProperty(param1.content,"name",param2);
         var _loc5_:ClassFactory = new ClassFactory(ReflectUtil.getTypeByProperty(param1,param2));
         if(param3 is Class)
         {
            _loc5_ = new ClassFactory(param3 as Class);
         }
         else if(param3 is ClassFactory)
         {
            _loc5_ = param3 as ClassFactory;
         }
         else if(param3 is Array)
         {
            _loc5_.params = (param3 as Array).concat();
         }
         else if(param3)
         {
            _loc5_.properties = param3;
         }
         if(_loc4_)
         {
            if(_loc5_.params)
            {
               _loc5_.params[0] = _loc4_;
            }
            else
            {
               _loc5_.params = [_loc4_];
            }
         }
         var _loc6_:GBase = _loc5_.newInstance();
         _loc6_.owner = param1;
         return _loc6_;
      }
      
      public static function destory(param1:GBase, param2:Boolean = false) : void
      {
         var _loc7_:DisplayObject = null;
         var _loc8_:String = null;
         var _loc3_:DisplayObject = param1.content;
         var _loc4_:Array = SearchUtil.findChildrenByClass(_loc3_,IGBase);
         var _loc5_:Object = ReflectUtil.getPropertyTypeList(param1,true);
         var _loc6_:int = 0;
         while(_loc6_ < _loc4_.length)
         {
            _loc7_ = _loc4_[_loc6_] as DisplayObject;
            if(_loc7_ is IGBase)
            {
               _loc8_ = _loc7_.name;
               if(param2 || Boolean(_loc5_[_loc8_]))
               {
                  (_loc7_ as IGBase).destory();
               }
            }
            _loc6_++;
         }
      }
      
      public static function autoBNHandlers(param1:DisplayObject, param2:Boolean = false, param3:Boolean = false) : void
      {
         var _loc5_:String = null;
         var _loc4_:Object = ReflectUtil.getPropertyTypeList(param1,true);
         for(_loc5_ in _loc4_)
         {
            if(Boolean(param1.hasOwnProperty(_loc5_)) && Boolean(param1.hasOwnProperty(_loc5_ + "Handler")) && param1[_loc5_ + "Handler"] is Function)
            {
               if(param1["p"] is GButtonBase)
               {
                  if(param2)
                  {
                     (param1["p"] as GButtonBase).removeEventListener(MouseEvent.CLICK,param1[_loc5_ + "Handler"]);
                  }
                  else
                  {
                     (param1["p"] as GButtonBase).addEventListener(MouseEvent.CLICK,param1[_loc5_ + "Handler"],false,0,param3);
                  }
               }
            }
         }
      }
      
      public static function getSkinByName(param1:DisplayObject, param2:String) : DisplayObject
      {
         return SearchUtil.findChildByProperty(param1,"name",param2);
      }
   }
}

