package ghostcat.skin
{
   import flash.filters.DropShadowFilter;
   import flash.geom.Point;
   import ghostcat.display.GNoScale;
   import ghostcat.parse.DisplayParse;
   import ghostcat.parse.graphics.GraphicsFill;
   import ghostcat.parse.graphics.GraphicsLineStyle;
   import ghostcat.parse.graphics.GraphicsRect;
   
   public class Arow<PERSON>kin extends GNoScale
   {
      
      private var _point:Point;
      
      private var radius:Number;
      
      private var borderColor:uint;
      
      private var borderThickness:Number;
      
      private var fillColor:uint;
      
      public function ArowSkin(param1:Number = 100, param2:Number = 20, param3:Point = null, param4:Number = 5, param5:uint = 0, param6:Number = 1, param7:uint = 16777215)
      {
         if(!param3)
         {
            param3 = new Point(0,-5);
         }
         DisplayParse.create([new GraphicsLineStyle(param6,param5),new GraphicsFill(param7)]).parse(this);
         this.filters = [new DropShadowFilter(4,45,0,0.5)];
         this.radius = param4;
         this.borderColor = param5;
         this.borderThickness = param5;
         this.fillColor = param7;
         this._point = param3;
         super();
         this.width = param1;
         this.height = param2;
      }
      
      public function get point() : Point
      {
         return this._point;
      }
      
      public function set point(param1:Point) : void
      {
         if(this._point.equals(param1))
         {
            return;
         }
         this._point = param1;
         invalidateSize();
      }
      
      override protected function updateDisplayList() : void
      {
         graphics.clear();
         DisplayParse.create([new GraphicsLineStyle(this.borderThickness,this.borderColor),new GraphicsFill(this.fillColor),new GraphicsRect(0,0,width,height,this.radius,this.radius,this.radius,this.radius,this.point)]).parse(this);
         super.updateDisplayList();
      }
   }
}

