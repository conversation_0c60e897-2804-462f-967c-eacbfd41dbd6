package ghostcat.display.movieclip
{
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import ghostcat.events.TimeLineEvent;
   
   public class TimeLine extends EventDispatcher
   {
      
      public var mc:MovieClip;
      
      public var metords:Dictionary = new Dictionary();
      
      public var prevFrame:int = -1;
      
      public var prevLabel:String;
      
      private var _labels:Array;
      
      public function TimeLine(param1:MovieClip)
      {
         super();
         this.mc = param1;
         param1.addEventListener(Event.ENTER_FRAME,this.enterFrameHandler);
         this._labels = param1.currentLabels.concat();
      }
      
      public function insertMetord(param1:*, param2:Function) : void
      {
         var _loc3_:Array = null;
         if(param1 is String)
         {
            param1 = this.getLabelStart(param1);
         }
         if(!this.metords[param1])
         {
            _loc3_ = [];
            this.metords[param1] = _loc3_;
         }
         else
         {
            _loc3_ = this.metords[param1];
         }
         _loc3_.push(param2);
      }
      
      public function removeMetord(param1:*, param2:Function = null) : void
      {
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         if(param1 is String)
         {
            param1 = this.getLabelStart(param1);
         }
         if(this.metords[param1])
         {
            _loc3_ = this.metords[param1];
            if(param2 != null)
            {
               _loc4_ = int(_loc3_.indexOf(param2));
               if(_loc4_ != -1)
               {
                  _loc3_.splice(_loc4_,1);
               }
               if(_loc3_.length == 0)
               {
                  delete this.metords[param1];
               }
            }
            else
            {
               delete this.metords[param1];
            }
         }
      }
      
      public function get curLabelName() : String
      {
         return this.mc.currentLabel;
      }
      
      public function get labels() : Array
      {
         return this._labels;
      }
      
      public function getLabelLength(param1:*) : int
      {
         var _loc2_:int = this.getLabelIndex(param1);
         if(_loc2_ + 1 < this.labels.length)
         {
            return this.labels[_loc2_ + 1].frame - this.labels[_loc2_].frame;
         }
         return this.mc.totalFrames - this.labels[_loc2_].frame;
      }
      
      public function getLabelStart(param1:*) : int
      {
         var _loc2_:int = this.getLabelIndex(param1);
         if(_loc2_ + 1 < this.labels.length)
         {
            return this.labels[_loc2_].frame;
         }
         return 1;
      }
      
      public function getLabelIndex(param1:*) : int
      {
         var _loc2_:int = int(this.labels.length - 1);
         while(_loc2_ >= 0)
         {
            if(param1 is String)
            {
               if(this.labels[_loc2_].name == param1)
               {
                  return _loc2_;
               }
            }
            else if(this.labels[_loc2_].frame < param1)
            {
               return _loc2_;
            }
            _loc2_--;
         }
         return -1;
      }
      
      public function gotoAndStop(param1:Object) : void
      {
         var _loc2_:int = 0;
         if(param1 is String)
         {
            _loc2_ = this.getLabelIndex(param1.toString());
            if(_loc2_ != -1)
            {
               this.mc.gotoAndStop(this.labels[_loc2_].frame);
            }
         }
         else if(param1 is Number)
         {
            this.mc.gotoAndStop(param1);
         }
      }
      
      public function gotoAndPlay(param1:Object) : void
      {
         var _loc2_:int = 0;
         if(param1 is String)
         {
            _loc2_ = this.getLabelIndex(param1.toString());
            if(_loc2_ != -1)
            {
               this.mc.gotoAndPlay(this.labels[_loc2_].frame);
            }
         }
         else if(param1 is Number)
         {
            this.mc.gotoAndPlay(param1);
         }
      }
      
      private function enterFrameHandler(param1:Event) : void
      {
         var _loc2_:TimeLineEvent = null;
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         if(this.prevFrame == this.mc.currentFrame)
         {
            return;
         }
         if(this.metords[this.mc.currentFrame])
         {
            _loc3_ = this.metords[this.mc.currentFrame];
            _loc4_ = 0;
            while(_loc4_ < _loc3_.length)
            {
               (_loc3_[_loc4_] as Function).call(this.mc);
               _loc4_++;
            }
         }
         if(this.mc.currentLabel != this.prevLabel)
         {
            _loc2_ = new TimeLineEvent(TimeLineEvent.LABEL_CHANGED);
            _loc2_.prevLabel = this.prevLabel;
            _loc2_.label = this.mc.currentLabel;
            _loc2_.prevFrame = this.prevFrame;
            _loc2_.frame = this.mc.currentFrame;
            dispatchEvent(_loc2_);
         }
         if(this.mc.currentFrame == this.mc.totalFrames)
         {
            _loc2_ = new TimeLineEvent(TimeLineEvent.TIMELINE_END);
            _loc2_.prevLabel = this.prevLabel;
            _loc2_.label = this.mc.currentLabel;
            _loc2_.prevFrame = this.prevFrame;
            _loc2_.frame = this.mc.currentFrame;
            dispatchEvent(_loc2_);
         }
         if(this.mc.currentFrame == 1)
         {
            _loc2_ = new TimeLineEvent(TimeLineEvent.TIMELINE_START);
            _loc2_.prevLabel = this.prevLabel;
            _loc2_.label = this.mc.currentLabel;
            _loc2_.prevFrame = this.prevFrame;
            _loc2_.frame = this.mc.currentFrame;
            dispatchEvent(_loc2_);
         }
         this.prevFrame = this.mc.currentFrame;
         this.prevLabel = this.mc.currentLabel;
      }
   }
}

