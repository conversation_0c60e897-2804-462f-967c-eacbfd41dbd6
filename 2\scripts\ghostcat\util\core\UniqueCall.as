package ghostcat.util.core
{
   import flash.utils.setTimeout;
   import ghostcat.events.TickEvent;
   import ghostcat.util.Tick;
   
   public class UniqueCall
   {
      
      public var dirty:Boolean = false;
      
      public var frame:Boolean = false;
      
      public var inv:int;
      
      public var handler:Function;
      
      protected var para:Array;
      
      private var tick:Tick = Tick.instance;
      
      public function UniqueCall(param1:Function, param2:Boolean = false, param3:Number = NaN)
      {
         super();
         this.handler = param1;
         this.frame = param2;
         this.inv = param3;
      }
      
      public function invalidate(... rest) : void
      {
         if(this.dirty)
         {
            return;
         }
         this.dirty = true;
         this.para = rest;
         if(this.frame)
         {
            this.tick.addEventListener(TickEvent.TICK,this.tickHandler);
         }
         else
         {
            setTimeout(this.vaildNow,this.inv);
         }
      }
      
      public function vaildNow() : void
      {
         if(this.para)
         {
            this.handler.apply(null,this.para);
         }
         else
         {
            this.handler();
         }
         this.dirty = false;
         this.para = null;
      }
      
      private function tickHandler(param1:TickEvent) : void
      {
         this.tick.removeEventListener(TickEvent.TICK,this.tickHandler);
         this.vaildNow();
      }
   }
}

