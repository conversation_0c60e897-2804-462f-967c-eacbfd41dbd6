package ghostcat.parse.graphics
{
   import flash.display.Graphics;
   import ghostcat.parse.DisplayParse;
   
   public class GraphicsFill extends DisplayParse implements IGraphicsFill
   {
      
      public var color:uint;
      
      public var alpha:Number = 1;
      
      public function GraphicsFill(param1:uint, param2:Number = NaN)
      {
         var _loc3_:Number = NaN;
         super();
         if(isNaN(param2))
         {
            _loc3_ = (param1 >> 24 & 0xFF) / 255;
            if(_loc3_ != 0)
            {
               param2 = _loc3_;
            }
            else
            {
               param2 = 1;
            }
         }
         this.color = param1;
         this.alpha = param2;
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         super.parseGraphics(param1);
         param1.beginFill(this.color,this.alpha);
      }
   }
}

