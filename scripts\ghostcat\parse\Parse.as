package ghostcat.parse
{
   import ghostcat.util.Util;
   
   public class Parse implements IParse
   {
      
      private var _parent:IParse;
      
      private var _children:Array = [];
      
      public function Parse()
      {
         super();
      }
      
      public static function create(param1:Array) : Parse
      {
         var _loc2_:Parse = new Parse();
         var _loc3_:int = 0;
         while(_loc3_ < param1.length)
         {
            _loc2_.addChild(param1[_loc3_]);
            _loc3_++;
         }
         return _loc2_;
      }
      
      public function parse(param1:*) : void
      {
         var _loc3_:int = 0;
         var _loc2_:Array = this.children;
         if(_loc2_)
         {
            _loc3_ = 0;
            while(_loc3_ < _loc2_.length)
            {
               (_loc2_[_loc3_] as IParse).parse(param1);
               _loc3_++;
            }
         }
      }
      
      public function set parent(param1:IParse) : void
      {
         this._parent = param1;
      }
      
      public function get parent() : IParse
      {
         return this._parent;
      }
      
      public function set children(param1:Array) : void
      {
         this._children = param1;
      }
      
      public function get children() : Array
      {
         return this._children;
      }
      
      public function addChild(param1:IParse) : void
      {
         this.children.push(param1);
         param1.parent = this;
      }
      
      public function removeChild(param1:IParse) : void
      {
         Util.remove(this.children,param1);
         param1.parent = null;
      }
   }
}

