package ghostcat.util
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.utils.getTimer;
   import ghostcat.events.TickEvent;
   import ghostcat.util.core.Singleton;
   
   public class Tick extends Singleton
   {
      
      public static var frameRate:Number = NaN;
      
      public static var MAX_INTERVAL:int = 300;
      
      private var displayObject:Sprite;
      
      private var prevTime:int;
      
      public var speed:Number = 1;
      
      public var pause:Boolean = false;
      
      public function Tick()
      {
         super();
         this.displayObject = new Sprite();
         this.displayObject.addEventListener(Event.ENTER_FRAME,this.enterFrameHandler);
      }
      
      public static function get instance() : Tick
      {
         return Singleton.getInstanceOrCreate(Tick) as Tick;
      }
      
      public function clear() : void
      {
         this.prevTime = 0;
      }
      
      private function enterFrameHandler(param1:Event) : void
      {
         var _loc3_:int = 0;
         var _loc4_:TickEvent = null;
         var _loc2_:int = getTimer();
         if(!this.pause)
         {
            if(this.prevTime == 0)
            {
               _loc3_ = 0;
            }
            else
            {
               _loc3_ = Math.min(_loc2_ - this.prevTime,MAX_INTERVAL);
               _loc4_ = new TickEvent(TickEvent.TICK);
               _loc4_.interval = _loc3_ * this.speed;
               dispatchEvent(_loc4_);
            }
         }
         this.prevTime = _loc2_;
      }
   }
}

