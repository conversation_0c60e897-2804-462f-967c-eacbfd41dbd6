package ghostcat.display.movieclip
{
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.TimerEvent;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   import flash.utils.Timer;
   
   public class Movie<PERSON>lip<PERSON>acher extends EventDispatcher
   {
      
      public var mc:MovieClip;
      
      public var rect:Rectangle;
      
      public var result:Array;
      
      public var readComplete:Boolean = false;
      
      private var readFrame:int;
      
      private var endFrame:int;
      
      private var timer:Timer;
      
      public function MovieClipCacher(param1:MovieClip, param2:Rectangle = null, param3:int = 1, param4:int = -1)
      {
         super();
         if(param1)
         {
            this.read(param1,param2,param3,param4);
         }
      }
      
      public function read(param1:MovieClip, param2:Rectangle = null, param3:int = 1, param4:int = -1) : void
      {
         this.mc = param1;
         this.rect = param2 ? param2 : param1.getBounds(param1);
         this.readFrame = param3;
         param1.gotoAndStop(param3);
         if(param4 == -1)
         {
            this.endFrame = param1.totalFrames;
         }
         else
         {
            this.endFrame = param3 + param4 - 1;
            if(this.endFrame > param1.totalFrames)
            {
               this.endFrame = param1.totalFrames;
            }
         }
         this.result = [];
         this.readComplete = false;
         this.timer = new Timer(0,int.MAX_VALUE);
         this.timer.addEventListener(TimerEvent.TIMER,this.timeHandler);
         this.timer.start();
      }
      
      private function timeHandler(param1:Event) : void
      {
         var _loc2_:BitmapData = null;
         var _loc3_:Matrix = null;
         if(this.mc.currentFrame >= this.readFrame)
         {
            _loc2_ = new BitmapData(Math.ceil(this.rect.width),Math.ceil(this.rect.height),true,0);
            if(this.rect)
            {
               _loc3_ = new Matrix();
               _loc3_.translate(-this.rect.x,-this.rect.y);
            }
            _loc2_.draw(this.mc,_loc3_);
            this.result.push(_loc2_);
            if(this.mc.currentFrame >= this.endFrame)
            {
               this.readCompleteHandler();
            }
            else
            {
               ++this.readFrame;
               this.mc.nextFrame();
            }
         }
      }
      
      private function readCompleteHandler() : void
      {
         this.timer.removeEventListener(TimerEvent.TIMER,this.timeHandler);
         this.timer.stop();
         this.timer = null;
         this.readComplete = true;
         dispatchEvent(new Event(Event.COMPLETE));
      }
      
      public function dispose() : void
      {
         var _loc1_:BitmapData = null;
         for each(_loc1_ in this.result.length)
         {
            _loc1_.dispose();
         }
      }
   }
}

