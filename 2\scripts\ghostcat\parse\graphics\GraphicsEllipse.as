package ghostcat.parse.graphics
{
   import flash.display.Graphics;
   import ghostcat.parse.DisplayParse;
   
   public class GraphicsEllipse extends DisplayParse
   {
      
      public var x:Number;
      
      public var y:Number;
      
      public var width:Number;
      
      public var height:Number;
      
      public function GraphicsEllipse(param1:Number, param2:Number, param3:Number, param4:Number)
      {
         super();
         this.x = param1;
         this.y = param2;
         this.width = param3;
         this.height = param4;
      }
      
      override public function parseGraphics(param1:Graphics) : void
      {
         super.parseGraphics(param1);
         if(this.width == this.height)
         {
            param1.drawCircle(this.x,this.y,this.width / 2);
         }
         else
         {
            param1.drawEllipse(this.x,this.y,this.width,this.height);
         }
      }
   }
}

