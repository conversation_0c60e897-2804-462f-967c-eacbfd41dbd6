package ghostcat.parse
{
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.DisplayObjectContainer;
   import flash.display.Graphics;
   import flash.display.Shape;
   import flash.display.Sprite;
   
   public class DisplayParse extends Parse
   {
      
      public function DisplayParse()
      {
         super();
      }
      
      public static function create(param1:Array) : DisplayParse
      {
         var _loc2_:DisplayParse = new DisplayParse();
         _loc2_.children = param1;
         return _loc2_;
      }
      
      public static function createSprite(param1:Array) : Sprite
      {
         var _loc2_:Sprite = new Sprite();
         create(param1).parse(_loc2_);
         return _loc2_;
      }
      
      public static function createShape(param1:Array) : Shape
      {
         var _loc2_:Shape = new Shape();
         create(param1).parse(_loc2_);
         return _loc2_;
      }
      
      override public function parse(param1:*) : void
      {
         super.parse(param1);
         if(param1 is DisplayObject)
         {
            this.parseDisplay(param1 as DisplayObject);
         }
         if(param1 is DisplayObjectContainer)
         {
            this.parseContainer(param1 as DisplayObjectContainer);
         }
         if(param1 is Graphics)
         {
            this.parseGraphics(param1 as Graphics);
         }
         if(param1 is BitmapData)
         {
            this.parseBitmapData(param1 as BitmapData);
         }
         var _loc2_:Graphics = param1 && Boolean(param1.hasOwnProperty("graphics")) ? param1["graphics"] as Graphics : null;
         if(_loc2_)
         {
            this.parseGraphics(_loc2_);
         }
      }
      
      public function parseGraphics(param1:Graphics) : void
      {
      }
      
      public function parseBitmapData(param1:BitmapData) : void
      {
      }
      
      public function parseContainer(param1:DisplayObjectContainer) : void
      {
      }
      
      public function parseDisplay(param1:DisplayObject) : void
      {
      }
      
      public function createSprite() : Sprite
      {
         var _loc1_:Sprite = new Sprite();
         this.parse(_loc1_);
         return _loc1_;
      }
      
      public function createShape() : Shape
      {
         var _loc1_:Shape = new Shape();
         this.parse(_loc1_);
         return _loc1_;
      }
   }
}

